{"file_contents": {"ai_agents.py": {"content": "import json\nimport os\nimport logging\nfrom typing import Any, Dict, List, Optional\nfrom openai import OpenAI\n\n# the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024.\n# do not change this unless explicitly requested by the user\nOPENAI_API_KEY = os.environ.get(\"OPENAI_API_KEY\")\nif not OPENAI_API_KEY:\n    logging.warning(\"OPENAI_API_KEY not found in environment variables\")\n\nopenai_client = OpenAI(api_key=OPENAI_API_KEY) if OPENAI_API_KEY else None\n\nclass PreprocessingAgent:\n    \"\"\"Base class for all preprocessing AI agents\"\"\"\n    \n    def __init__(self, agent_type):\n        self.agent_type = agent_type\n        self.client = openai_client\n    \n    def get_system_prompt(self) -> str:\n        \"\"\"Override in subclasses to provide specific system prompts\"\"\"\n        return \"You are an expert CAE preprocessing assistant.\"\n    \n    def make_request(self, user_message: str, context: Optional[str] = None) -> Dict[str, Any]:\n        \"\"\"Make a request to the OpenAI API with proper error handling\"\"\"\n        if not self.client:\n            return {\"error\": \"OpenAI API key not configured\"}\n        \n        try:\n            messages: List[Dict[str, str]] = [\n                {\"role\": \"system\", \"content\": self.get_system_prompt()},\n            ]\n            \n            if context:\n                messages.append({\"role\": \"system\", \"content\": f\"Context: {context}\"})\n            \n            messages.append({\"role\": \"user\", \"content\": user_message})\n            \n            # Use type: ignore to suppress TypeScript-like type checking warnings\n            response = self.client.chat.completions.create(  # type: ignore\n                model=\"gpt-4o\",\n                messages=messages,\n                response_format={\"type\": \"json_object\"},\n                temperature=0.7,\n                max_tokens=1000\n            )\n            \n            content = response.choices[0].message.content\n            if content:\n                return json.loads(content)\n            else:\n                return {\"error\": \"No content received from AI\"}\n        \n        except Exception as e:\n            logging.error(f\"AI Agent Error ({self.agent_type}): {str(e)}\")\n            return {\"error\": f\"AI processing failed: {str(e)}\"}\n\nclass GeometryAgent(PreprocessingAgent):\n    \"\"\"AI agent specialized in CAD geometry preparation and simplification\"\"\"\n    \n    def __init__(self):\n        super().__init__(\"geometry\")\n    \n    def get_system_prompt(self) -> str:\n        return \"\"\"You are an expert CAE geometry preprocessing specialist. Your role is to guide engineers through the critical process of preparing CAD geometry for simulation analysis.\n\nKey responsibilities:\n1. Analyze CAD models for simulation readiness\n2. Recommend defeaturing strategies (removing small holes, fillets, chamfers)\n3. Suggest mid-surface creation for thin-walled structures\n4. Propose envelope geometry for complex assemblies\n5. Identify potential meshing challenges\n\nAlways respond in JSON format with fields: recommendations, defeaturing_steps, potential_issues, mesh_considerations, and confidence_score.\"\"\"\n    \n    def analyze_geometry(self, file_info, analysis_objectives):\n        \"\"\"Analyze uploaded geometry and provide preprocessing recommendations\"\"\"\n        prompt = f\"\"\"\n        Analyze this CAD geometry for simulation preprocessing:\n        \n        File: {file_info.get('filename', 'Unknown')}\n        Type: {file_info.get('file_type', 'Unknown')}\n        Size: {file_info.get('file_size', 'Unknown')} bytes\n        Analysis Objectives: {analysis_objectives}\n        \n        Provide specific recommendations for geometry preparation including:\n        1. Required defeaturing operations\n        2. Mesh density considerations\n        3. Potential simulation challenges\n        4. Recommended simplification strategies\n        \n        Respond in JSON format.\n        \"\"\"\n        \n        return self.make_request(prompt)\n\nclass MeshAgent(PreprocessingAgent):\n    \"\"\"AI agent specialized in mesh generation and quality assurance\"\"\"\n    \n    def __init__(self):\n        super().__init__(\"mesh\")\n    \n    def get_system_prompt(self) -> str:\n        return \"\"\"You are an expert mesh generation specialist for CAE analysis. Your expertise covers:\n\n1. Mesh type selection (tetrahedral, hexahedral, hybrid)\n2. Element size optimization\n3. Mesh quality metrics (aspect ratio, skewness, Jacobian)\n4. Local refinement strategies\n5. Convergence analysis recommendations\n\nAlways respond in JSON format with fields: mesh_strategy, element_types, sizing_recommendations, quality_targets, refinement_zones, and computational_cost_estimate.\"\"\"\n    \n    def recommend_mesh_strategy(self, geometry_info, physics_type, computational_resources):\n        \"\"\"Recommend optimal meshing strategy based on geometry and physics\"\"\"\n        prompt = f\"\"\"\n        Recommend meshing strategy for this simulation:\n        \n        Geometry Info: {geometry_info}\n        Physics Type: {physics_type}\n        Computational Resources: {computational_resources}\n        \n        Provide recommendations for:\n        1. Optimal mesh type and element selection\n        2. Global and local sizing strategies\n        3. Quality targets and metrics to monitor\n        4. Expected computational cost\n        5. Convergence study approach\n        \n        Respond in JSON format.\n        \"\"\"\n        \n        return self.make_request(prompt)\n\nclass MaterialAgent(PreprocessingAgent):\n    \"\"\"AI agent specialized in material property assignment\"\"\"\n    \n    def __init__(self):\n        super().__init__(\"materials\")\n    \n    def get_system_prompt(self) -> str:\n        return \"\"\"You are an expert materials engineer specializing in CAE material property assignment. Your expertise includes:\n\n1. Material model selection (linear, nonlinear, anisotropic)\n2. Property validation and sourcing\n3. Environmental factor consideration\n4. Temperature-dependent properties\n5. Safety factor recommendations\n\nAlways respond in JSON format with fields: material_recommendations, property_sources, environmental_considerations, validation_requirements, and confidence_assessment.\"\"\"\n    \n    def recommend_materials(self, component_info, operating_conditions, safety_requirements):\n        \"\"\"Recommend appropriate materials and properties\"\"\"\n        prompt = f\"\"\"\n        Recommend materials and properties for this component:\n        \n        Component: {component_info}\n        Operating Conditions: {operating_conditions}\n        Safety Requirements: {safety_requirements}\n        \n        Provide recommendations for:\n        1. Suitable material candidates\n        2. Critical properties to define\n        3. Property data sources and validation\n        4. Environmental factor considerations\n        5. Safety margins and factors\n        \n        Respond in JSON format.\n        \"\"\"\n        \n        return self.make_request(prompt)\n\nclass PhysicsAgent(PreprocessingAgent):\n    \"\"\"AI agent specialized in physics and boundary condition definition\"\"\"\n    \n    def __init__(self):\n        super().__init__(\"physics\")\n    \n    def get_system_prompt(self) -> str:\n        return \"\"\"You are an expert simulation physicist specializing in boundary condition definition for CAE analysis. Your expertise covers:\n\n1. Load and constraint definition\n2. Boundary condition selection and validation\n3. Symmetry and periodicity identification\n4. Convergence criteria establishment\n5. Solution verification strategies\n\nAlways respond in JSON format with fields: boundary_conditions, load_definitions, symmetry_opportunities, solver_settings, and verification_plan.\"\"\"\n    \n    def define_physics_setup(self, analysis_type, real_world_conditions, geometry_features):\n        \"\"\"Define appropriate physics setup and boundary conditions\"\"\"\n        prompt = f\"\"\"\n        Define physics setup for this simulation:\n        \n        Analysis Type: {analysis_type}\n        Real-world Conditions: {real_world_conditions}\n        Geometry Features: {geometry_features}\n        \n        Provide recommendations for:\n        1. Appropriate boundary conditions\n        2. Load definition and application\n        3. Symmetry and periodicity opportunities\n        4. Solver settings and convergence criteria\n        5. Result verification approach\n        \n        Respond in JSON format.\n        \"\"\"\n        \n        return self.make_request(prompt)\n\n# Agent factory for easy instantiation\ndef get_agent(agent_type):\n    \"\"\"Factory function to get the appropriate AI agent\"\"\"\n    agents = {\n        'geometry': GeometryAgent,\n        'mesh': MeshAgent,\n        'materials': MaterialAgent,\n        'physics': PhysicsAgent\n    }\n    \n    agent_class = agents.get(agent_type)\n    if agent_class:\n        return agent_class()\n    else:\n        raise ValueError(f\"Unknown agent type: {agent_type}\")\n", "size_bytes": 8821}, "app.py": {"content": "import os\nimport logging\nfrom flask import Flask\nfrom flask_sqlalchemy import SQLAlchemy\nfrom sqlalchemy.orm import DeclarativeBase\nfrom werkzeug.middleware.proxy_fix import ProxyFix\n\n# Configure logging for debugging\nlogging.basicConfig(level=logging.DEBUG)\n\nclass Base(DeclarativeBase):\n    pass\n\ndb = SQLAlchemy(model_class=Base)\n\n# Create the app\napp = Flask(__name__)\napp.secret_key = os.environ.get(\"SESSION_SECRET\", \"dev-secret-key-for-ensimu\")\napp.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)\n\n# Configure file uploads\napp.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size\napp.config['UPLOAD_FOLDER'] = 'uploads'\n\n# Ensure upload directory exists\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\n\n# Configure the database\napp.config[\"SQLALCHEMY_DATABASE_URI\"] = os.environ.get(\"DATABASE_URL\", \"sqlite:///ensimu.db\")\napp.config[\"SQLALCHEMY_ENGINE_OPTIONS\"] = {\n    \"pool_recycle\": 300,\n    \"pool_pre_ping\": True,\n}\n\n# Initialize the app with the extension\ndb.init_app(app)\n\nwith app.app_context():\n    # Import models to ensure tables are created\n    import models  # noqa: F401\n    db.create_all()\n    \n    # Import and register routes\n    import routes  # noqa: F401\n", "size_bytes": 1215}, "engineering_utils.py": {"content": "\"\"\"Engineering utilities and standards for CAE preprocessing\"\"\"\n\nimport re\nimport os\nfrom werkzeug.utils import secure_filename\n\n# Supported CAD file formats\nSUPPORTED_CAD_FORMATS = {\n    '.step': 'STEP',\n    '.stp': 'STEP', \n    '.iges': 'IGES',\n    '.igs': 'IGES',\n    '.stl': 'STL',\n    '.obj': 'OBJ',\n    '.ply': 'PLY',\n    '.x_t': 'Parasolid',\n    '.x_b': 'Parasolid'\n}\n\n# Common engineering materials database\nENGINEERING_MATERIALS = {\n    'metals': {\n        'Aluminum 6061-T6': {\n            'youngs_modulus': 68.9e9,  # Pa\n            'poisson_ratio': 0.33,\n            'density': 2700,  # kg/m³\n            'yield_strength': 276e6,  # Pa\n            'ultimate_strength': 310e6,  # Pa\n            'thermal_conductivity': 167,  # W/m·K\n            'specific_heat': 896,  # J/kg·K\n            'thermal_expansion': 23.6e-6  # 1/K\n        },\n        'Steel AISI 1020': {\n            'youngs_modulus': 200e9,\n            'poisson_ratio': 0.29,\n            'density': 7850,\n            'yield_strength': 350e6,\n            'ultimate_strength': 420e6,\n            'thermal_conductivity': 51.9,\n            'specific_heat': 486,\n            'thermal_expansion': 11.7e-6\n        },\n        'Titanium Ti-6Al-4V': {\n            'youngs_modulus': 113.8e9,\n            'poisson_ratio': 0.342,\n            'density': 4430,\n            'yield_strength': 880e6,\n            'ultimate_strength': 950e6,\n            'thermal_conductivity': 6.7,\n            'specific_heat': 526,\n            'thermal_expansion': 8.6e-6\n        }\n    },\n    'polymers': {\n        'ABS Plastic': {\n            'youngs_modulus': 2.3e9,\n            'poisson_ratio': 0.35,\n            'density': 1050,\n            'yield_strength': 40e6,\n            'ultimate_strength': 45e6,\n            'thermal_conductivity': 0.25,\n            'specific_heat': 1386,\n            'thermal_expansion': 90e-6\n        },\n        'Nylon 6/6': {\n            'youngs_modulus': 2.9e9,\n            'poisson_ratio': 0.39,\n            'density': 1140,\n            'yield_strength': 75e6,\n            'ultimate_strength': 85e6,\n            'thermal_conductivity': 0.25,\n            'specific_heat': 1670,\n            'thermal_expansion': 80e-6\n        }\n    }\n}\n\n# Mesh quality thresholds\nMESH_QUALITY_STANDARDS = {\n    'excellent': {\n        'aspect_ratio_max': 3.0,\n        'skewness_max': 0.25,\n        'jacobian_min': 0.7\n    },\n    'good': {\n        'aspect_ratio_max': 5.0,\n        'skewness_max': 0.5,\n        'jacobian_min': 0.5\n    },\n    'acceptable': {\n        'aspect_ratio_max': 10.0,\n        'skewness_max': 0.8,\n        'jacobian_min': 0.3\n    }\n}\n\ndef is_supported_cad_file(filename):\n    \"\"\"Check if the uploaded file is a supported CAD format\"\"\"\n    ext = os.path.splitext(filename.lower())[1]\n    return ext in SUPPORTED_CAD_FORMATS\n\ndef get_file_format_info(filename):\n    \"\"\"Get information about the CAD file format\"\"\"\n    ext = os.path.splitext(filename.lower())[1]\n    if ext in SUPPORTED_CAD_FORMATS:\n        return {\n            'extension': ext,\n            'format': SUPPORTED_CAD_FORMATS[ext],\n            'supported': True\n        }\n    return {\n        'extension': ext,\n        'format': 'Unknown',\n        'supported': False\n    }\n\ndef secure_upload_filename(filename):\n    \"\"\"Generate a secure filename for uploaded files\"\"\"\n    return secure_filename(filename)\n\ndef estimate_mesh_complexity(file_size_bytes):\n    \"\"\"Estimate mesh complexity based on file size\"\"\"\n    size_mb = file_size_bytes / (1024 * 1024)\n    \n    if size_mb < 1:\n        return 'simple'\n    elif size_mb < 10:\n        return 'moderate'\n    elif size_mb < 50:\n        return 'complex'\n    else:\n        return 'very_complex'\n\ndef get_material_by_name(material_name):\n    \"\"\"Retrieve material properties by name\"\"\"\n    for category, materials in ENGINEERING_MATERIALS.items():\n        if material_name in materials:\n            return {\n                'category': category,\n                'properties': materials[material_name]\n            }\n    return None\n\ndef validate_mesh_quality(metrics):\n    \"\"\"Validate mesh quality against engineering standards\"\"\"\n    for standard, thresholds in MESH_QUALITY_STANDARDS.items():\n        passes_all = True\n        \n        if metrics.get('aspect_ratio', 0) > thresholds['aspect_ratio_max']:\n            passes_all = False\n        if metrics.get('skewness', 0) > thresholds['skewness_max']:\n            passes_all = False\n        if metrics.get('jacobian', 1) < thresholds['jacobian_min']:\n            passes_all = False\n            \n        if passes_all:\n            return standard\n    \n    return 'poor'\n\ndef generate_preprocessing_checklist(analysis_type):\n    \"\"\"Generate a preprocessing checklist based on analysis type\"\"\"\n    base_checklist = [\n        \"Import and inspect CAD geometry\",\n        \"Identify and remove unnecessary features\",\n        \"Check for geometric errors and inconsistencies\",\n        \"Define materials and properties\",\n        \"Generate appropriate mesh\",\n        \"Validate mesh quality\",\n        \"Apply boundary conditions\",\n        \"Define loads and constraints\",\n        \"Review solver settings\",\n        \"Perform sanity checks\"\n    ]\n    \n    type_specific = {\n        'structural': [\n            \"Verify support conditions represent reality\",\n            \"Check load path continuity\",\n            \"Consider stress concentrations\",\n            \"Validate material nonlinearity if applicable\"\n        ],\n        'thermal': [\n            \"Define heat sources and sinks\", \n            \"Set convection and radiation boundaries\",\n            \"Check thermal contact definitions\",\n            \"Verify temperature-dependent properties\"\n        ],\n        'fluid': [\n            \"Define inlet and outlet conditions\",\n            \"Set wall boundary conditions\",\n            \"Check fluid domain closure\",\n            \"Consider turbulence modeling\"\n        ]\n    }\n    \n    if analysis_type.lower() in type_specific:\n        base_checklist.extend(type_specific[analysis_type.lower()])\n    \n    return base_checklist\n\ndef get_solver_recommendations(analysis_type, complexity):\n    \"\"\"Get solver recommendations based on analysis type and complexity\"\"\"\n    recommendations = {\n        'structural': {\n            'simple': {\n                'solver': 'Direct Sparse',\n                'convergence_criteria': 1e-6,\n                'max_iterations': 100\n            },\n            'complex': {\n                'solver': 'Iterative PCG',\n                'convergence_criteria': 1e-8,\n                'max_iterations': 1000\n            }\n        },\n        'thermal': {\n            'simple': {\n                'solver': 'Direct',\n                'convergence_criteria': 1e-6,\n                'max_iterations': 50\n            },\n            'complex': {\n                'solver': 'Iterative',\n                'convergence_criteria': 1e-8,\n                'max_iterations': 500\n            }\n        }\n    }\n    \n    return recommendations.get(analysis_type, {}).get(complexity, {\n        'solver': 'Auto-select',\n        'convergence_criteria': 1e-6,\n        'max_iterations': 200\n    })\n", "size_bytes": 7095}, "main.py": {"content": "from app import app  # noqa: F401\n\nif __name__ == '__main__':\n    app.run(host='0.0.0.0', port=5000, debug=True)\n", "size_bytes": 113}, "models.py": {"content": "from datetime import datetime\nfrom app import db\nfrom sqlalchemy import Text, JSON\n\nclass Project(db.Model):\n    \"\"\"Model for engineering simulation projects\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(200), nullable=False)\n    description = db.Column(Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    status = db.Column(db.String(50), default='created')\n    \n    # Preprocessing progress tracking\n    geometry_status = db.Column(db.String(50), default='pending')\n    mesh_status = db.Column(db.String(50), default='pending')\n    materials_status = db.Column(db.String(50), default='pending')\n    physics_status = db.Column(db.String(50), default='pending')\n    \n    # Store preprocessing configurations as JSON\n    geometry_config = db.Column(JSON)\n    mesh_config = db.Column(JSON)\n    materials_config = db.Column(JSON)\n    physics_config = db.Column(JSON)\n    \n    # AI recommendations and insights\n    ai_recommendations = db.Column(JSON)\n    \n    def __repr__(self):\n        return f'<Project {self.name}>'\n\nclass UploadedFile(db.Model):\n    \"\"\"Model for uploaded CAD files and simulation data\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    filename = db.Column(db.String(200), nullable=False)\n    original_filename = db.Column(db.String(200), nullable=False)\n    file_path = db.Column(db.String(500), nullable=False)\n    file_type = db.Column(db.String(50), nullable=False)\n    file_size = db.Column(db.Integer)\n    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Analysis results from AI agents\n    analysis_results = db.Column(JSON)\n    \n    project = db.relationship('Project', backref=db.backref('files', lazy=True))\n\nclass MaterialProperty(db.Model):\n    \"\"\"Database of engineering materials and their properties\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(200), nullable=False)\n    category = db.Column(db.String(100), nullable=False)\n    \n    # Mechanical properties\n    youngs_modulus = db.Column(db.Float)\n    poisson_ratio = db.Column(db.Float)\n    density = db.Column(db.Float)\n    yield_strength = db.Column(db.Float)\n    ultimate_strength = db.Column(db.Float)\n    \n    # Thermal properties\n    thermal_conductivity = db.Column(db.Float)\n    specific_heat = db.Column(db.Float)\n    thermal_expansion = db.Column(db.Float)\n    \n    # Additional properties as JSON for flexibility\n    additional_properties = db.Column(JSON)\n    \n    # Source and validation information\n    data_source = db.Column(db.String(200))\n    validated = db.Column(db.Boolean, default=False)\n    \n    def __repr__(self):\n        return f'<Material {self.name}>'\n\nclass AISession(db.Model):\n    \"\"\"Track AI agent sessions and conversations\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    agent_type = db.Column(db.String(100), nullable=False)  # geometry, mesh, materials, physics\n    session_data = db.Column(JSON)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    completed = db.Column(db.Boolean, default=False)\n    \n    project = db.relationship('Project', backref=db.backref('ai_sessions', lazy=True))\n", "size_bytes": 3422}, "pyproject.toml": {"content": "[project]\nname = \"repl-nix-workspace\"\nversion = \"0.1.0\"\ndescription = \"Add your description here\"\nrequires-python = \">=3.11\"\ndependencies = [\n    \"email-validator>=2.2.0\",\n    \"flask>=3.1.1\",\n    \"flask-sqlalchemy>=3.1.1\",\n    \"gunicorn>=23.0.0\",\n    \"openai>=1.99.9\",\n    \"psycopg2-binary>=2.9.10\",\n]\n", "size_bytes": 302}, "replit.md": {"content": "# Ensimu.space - AI-Powered CAE Preprocessing Platform\n\n## Overview\n\nEnsimu.space is an intelligent Computer-Aided Engineering (CAE) preprocessing platform that leverages AI agents to streamline the complex workflow of simulation setup. The platform automates and guides users through the four critical pillars of CAE preprocessing: geometry preparation, mesh generation, material assignment, and physics definition. Built as a Flask web application, it combines domain expertise in engineering simulation with OpenAI's language models to provide expert-level guidance and automation for simulation engineers.\n\nThe platform addresses the time-intensive nature of simulation preprocessing, which traditionally consumes the majority of an analyst's time. By implementing a multi-agent AI workflow, it transforms tacit engineering knowledge into accessible, automated guidance that helps both novice and experienced engineers set up high-quality simulation models.\n\n## User Preferences\n\nPreferred communication style: Simple, everyday language.\n\n## System Architecture\n\n### Web Application Framework\n- **Flask Backend**: Core web application built with Flask, providing RESTful endpoints and server-side logic\n- **SQLAlchemy ORM**: Database abstraction layer using SQLAlchemy with support for multiple database backends\n- **Template Engine**: Jinja2 templating for dynamic HTML generation with Bootstrap 5 dark theme UI\n- **File Upload Handling**: Werkzeug-based secure file upload system with support for large CAD files (up to 100MB)\n\n### AI Agent Architecture\n- **Multi-Agent System**: Specialized AI agents for each preprocessing domain (geometry, mesh, materials, physics)\n- **OpenAI Integration**: GPT-4o model integration for natural language processing and domain-specific recommendations\n- **Agent Base Class**: Extensible `PreprocessingAgent` base class enabling consistent AI interaction patterns\n- **Contextual Processing**: Context-aware AI responses that incorporate project-specific information and engineering domain knowledge\n\n### Data Storage Design\n- **Project-Centric Model**: Core `Project` entity tracking preprocessing progress across all four pillars\n- **File Management**: `UploadedFile` model for CAD file metadata and analysis results storage\n- **Configuration Storage**: JSON-based storage for preprocessing configurations (geometry, mesh, materials, physics)\n- **Progress Tracking**: Status fields for each preprocessing step with granular progress monitoring\n\n### Frontend Architecture\n- **Progressive Enhancement**: JavaScript-enhanced user interface with graceful degradation\n- **Responsive Design**: Bootstrap 5-based responsive layout optimized for engineering workflows\n- **Real-time Feedback**: AJAX-based AI agent interactions with loading states and progress indicators\n- **Engineering Visualization**: Chart.js integration for mesh quality metrics and material property visualization\n\n### Engineering Domain Integration\n- **CAD Format Support**: Multi-format CAD file support (STEP, IGES, STL, OBJ, PLY, Parasolid)\n- **Materials Database**: Comprehensive engineering materials database with mechanical, thermal, and physical properties\n- **Preprocessing Utilities**: Domain-specific utilities for geometry analysis, mesh complexity estimation, and solver recommendations\n- **Industry Standards**: Implementation of engineering best practices and quality metrics for simulation setup\n\n## External Dependencies\n\n### AI and Machine Learning\n- **OpenAI API**: GPT-4o model for natural language processing and engineering domain expertise\n- **Environment Configuration**: API key management through environment variables\n\n### Web Framework and Database\n- **Flask Ecosystem**: Flask core framework with SQLAlchemy ORM for database operations\n- **Database Support**: SQLite for development with PostgreSQL compatibility for production deployment\n- **File Handling**: Werkzeug utilities for secure file upload and management\n\n### Frontend Libraries\n- **Bootstrap 5**: UI framework with Replit dark theme integration for consistent styling\n- **Feather Icons**: Lightweight icon library for engineering-focused iconography\n- **Chart.js**: Data visualization library for mesh quality metrics and engineering data display\n\n### Engineering and CAD Integration\n- **File Format Support**: Built-in support for major CAD formats without external CAD kernel dependencies\n- **Materials Database**: Integrated materials library with properties for common engineering materials (metals, polymers, composites)\n- **Engineering Standards**: Implementation of industry-standard mesh quality metrics and simulation best practices\n\n### Development and Deployment\n- **Python Standard Library**: Core Python libraries for file handling, JSON processing, and logging\n- **Environment Management**: Environment variable-based configuration for secure API key and database URL management\n- **WSGI Deployment**: ProxyFix middleware for production deployment behind reverse proxies", "size_bytes": 4983}, "routes.py": {"content": "import os\nimport logging\nfrom flask import render_template, request, redirect, url_for, flash, jsonify, current_app\nfrom werkzeug.utils import secure_filename\nfrom app import app, db\nfrom models import Project, UploadedFile, MaterialProperty, AISession\nfrom ai_agents import get_agent\nfrom engineering_utils import (\n    is_supported_cad_file, get_file_format_info, estimate_mesh_complexity,\n    ENGINEERING_MATERIALS, generate_preprocessing_checklist, get_solver_recommendations\n)\n\***********('/')\ndef index():\n    \"\"\"Main landing page\"\"\"\n    recent_projects = Project.query.order_by(Project.updated_at.desc()).limit(5).all()\n    return render_template('index.html', recent_projects=recent_projects)\n\***********('/upload', methods=['GET', 'POST'])\ndef upload():\n    \"\"\"File upload and project creation\"\"\"\n    if request.method == 'POST':\n        # Create new project\n        project_name = request.form.get('project_name')\n        project_description = request.form.get('project_description')\n        analysis_type = request.form.get('analysis_type')\n        \n        if not project_name:\n            flash('Project name is required', 'error')\n            return render_template('upload.html')\n        \n        project = Project()\n        project.name = project_name\n        project.description = project_description\n        project.status = 'uploading'\n        db.session.add(project)\n        db.session.commit()\n        \n        # Handle file uploads\n        uploaded_files = request.files.getlist('files')\n        for file in uploaded_files:\n            if file and file.filename:\n                if not is_supported_cad_file(file.filename):\n                    flash(f'Unsupported file format: {file.filename}', 'warning')\n                    continue\n                \n                filename = secure_filename(file.filename)\n                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], f\"{project.id}_{filename}\")\n                file.save(file_path)\n                \n                # Save file record\n                uploaded_file = UploadedFile()\n                uploaded_file.project_id = project.id\n                uploaded_file.filename = filename\n                uploaded_file.original_filename = file.filename\n                uploaded_file.file_path = file_path\n                uploaded_file.file_type = get_file_format_info(file.filename)['format']\n                uploaded_file.file_size = os.path.getsize(file_path)\n                db.session.add(uploaded_file)\n        \n        project.status = 'uploaded'\n        db.session.commit()\n        \n        flash('Project created and files uploaded successfully!', 'success')\n        return redirect(url_for('preprocessing', project_id=project.id))\n    \n    return render_template('upload.html')\n\***********('/project/<int:project_id>/preprocessing')\ndef preprocessing(project_id):\n    \"\"\"Main preprocessing workflow dashboard\"\"\"\n    project = Project.query.get_or_404(project_id)\n    files = UploadedFile.query.filter_by(project_id=project_id).all()\n    \n    # Calculate overall progress\n    statuses = [project.geometry_status, project.mesh_status, \n                project.materials_status, project.physics_status]\n    completed = sum(1 for status in statuses if status == 'completed')\n    progress = (completed / 4) * 100\n    \n    return render_template('preprocessing.html', \n                         project=project, files=files, progress=progress)\n\***********('/project/<int:project_id>/geometry')\ndef geometry(project_id):\n    \"\"\"Geometry preparation and simplification workflow\"\"\"\n    project = Project.query.get_or_404(project_id)\n    files = UploadedFile.query.filter_by(project_id=project_id).all()\n    \n    return render_template('geometry.html', project=project, files=files)\n\***********('/api/analyze_geometry', methods=['POST'])\ndef analyze_geometry():\n    \"\"\"API endpoint for AI-powered geometry analysis\"\"\"\n    data = request.get_json()\n    project_id = data.get('project_id')\n    analysis_objectives = data.get('analysis_objectives', 'General structural analysis')\n    \n    project = Project.query.get_or_404(project_id)\n    files = UploadedFile.query.filter_by(project_id=project_id).all()\n    \n    if not files:\n        return jsonify({'error': 'No files uploaded for analysis'}), 400\n    \n    try:\n        geometry_agent = get_agent('geometry')\n        \n        results = []\n        for file in files:\n            file_info = {\n                'filename': file.original_filename,\n                'file_type': file.file_type,\n                'file_size': file.file_size\n            }\n            \n            analysis = geometry_agent.analyze_geometry(file_info, analysis_objectives)\n            results.append({\n                'file_id': file.id,\n                'filename': file.original_filename,\n                'analysis': analysis\n            })\n            \n            # Update file with analysis results\n            file.analysis_results = analysis\n        \n        # Update project geometry status\n        project.geometry_status = 'analyzed'\n        project.geometry_config = {\n            'analysis_objectives': analysis_objectives,\n            'analysis_results': results\n        }\n        \n        db.session.commit()\n        \n        return jsonify({\n            'status': 'success',\n            'results': results\n        })\n        \n    except Exception as e:\n        logging.error(f\"Geometry analysis error: {str(e)}\")\n        return jsonify({'error': str(e)}), 500\n\***********('/project/<int:project_id>/meshing')\ndef meshing(project_id):\n    \"\"\"Mesh generation and quality assurance workflow\"\"\"\n    project = Project.query.get_or_404(project_id)\n    files = UploadedFile.query.filter_by(project_id=project_id).all()\n    \n    # Estimate mesh complexity for recommendations\n    total_complexity = 'simple'\n    for file in files:\n        complexity = estimate_mesh_complexity(file.file_size)\n        if complexity in ['complex', 'very_complex']:\n            total_complexity = complexity\n    \n    return render_template('meshing.html', \n                         project=project, files=files, complexity=total_complexity)\n\***********('/api/recommend_mesh', methods=['POST'])\ndef recommend_mesh():\n    \"\"\"API endpoint for AI-powered mesh recommendations\"\"\"\n    data = request.get_json()\n    project_id = data.get('project_id')\n    physics_type = data.get('physics_type', 'structural')\n    computational_resources = data.get('computational_resources', 'standard')\n    \n    project = Project.query.get_or_404(project_id)\n    \n    try:\n        mesh_agent = get_agent('mesh')\n        \n        geometry_info = project.geometry_config or {}\n        recommendations = mesh_agent.recommend_mesh_strategy(\n            geometry_info, physics_type, computational_resources\n        )\n        \n        # Update project mesh configuration\n        project.mesh_status = 'planned'\n        project.mesh_config = {\n            'physics_type': physics_type,\n            'computational_resources': computational_resources,\n            'recommendations': recommendations\n        }\n        \n        db.session.commit()\n        \n        return jsonify({\n            'status': 'success',\n            'recommendations': recommendations\n        })\n        \n    except Exception as e:\n        logging.error(f\"Mesh recommendation error: {str(e)}\")\n        return jsonify({'error': str(e)}), 500\n\***********('/project/<int:project_id>/materials')\ndef materials(project_id):\n    \"\"\"Material property assignment workflow\"\"\"\n    project = Project.query.get_or_404(project_id)\n    \n    # Get available materials from database and built-in library\n    db_materials = MaterialProperty.query.all()\n    builtin_materials = ENGINEERING_MATERIALS\n    \n    return render_template('materials.html', \n                         project=project, \n                         db_materials=db_materials,\n                         builtin_materials=builtin_materials)\n\***********('/api/recommend_materials', methods=['POST'])\ndef recommend_materials():\n    \"\"\"API endpoint for AI-powered material recommendations\"\"\"\n    data = request.get_json()\n    project_id = data.get('project_id')\n    component_info = data.get('component_info', 'General engineering component')\n    operating_conditions = data.get('operating_conditions', 'Standard environment')\n    safety_requirements = data.get('safety_requirements', 'Standard safety factors')\n    \n    project = Project.query.get_or_404(project_id)\n    \n    try:\n        material_agent = get_agent('materials')\n        \n        recommendations = material_agent.recommend_materials(\n            component_info, operating_conditions, safety_requirements\n        )\n        \n        # Update project materials configuration\n        project.materials_status = 'recommended'\n        project.materials_config = {\n            'component_info': component_info,\n            'operating_conditions': operating_conditions,\n            'safety_requirements': safety_requirements,\n            'recommendations': recommendations\n        }\n        \n        db.session.commit()\n        \n        return jsonify({\n            'status': 'success',\n            'recommendations': recommendations\n        })\n        \n    except Exception as e:\n        logging.error(f\"Material recommendation error: {str(e)}\")\n        return jsonify({'error': str(e)}), 500\n\***********('/project/<int:project_id>/physics')\ndef physics(project_id):\n    \"\"\"Physics and boundary condition definition workflow\"\"\"\n    project = Project.query.get_or_404(project_id)\n    \n    # Generate preprocessing checklist based on analysis type\n    analysis_type = 'structural'  # Default, should be determined from project config\n    if project.mesh_config:\n        analysis_type = project.mesh_config.get('physics_type', 'structural')\n    \n    checklist = generate_preprocessing_checklist(analysis_type)\n    solver_recommendations = get_solver_recommendations(analysis_type, 'moderate')\n    \n    return render_template('physics.html', \n                         project=project,\n                         checklist=checklist,\n                         solver_recommendations=solver_recommendations)\n\***********('/api/define_physics', methods=['POST'])\ndef define_physics():\n    \"\"\"API endpoint for AI-powered physics setup\"\"\"\n    data = request.get_json()\n    project_id = data.get('project_id')\n    analysis_type = data.get('analysis_type', 'structural')\n    real_world_conditions = data.get('real_world_conditions', 'Standard loading')\n    geometry_features = data.get('geometry_features', 'General geometry')\n    \n    project = Project.query.get_or_404(project_id)\n    \n    try:\n        physics_agent = get_agent('physics')\n        \n        physics_setup = physics_agent.define_physics_setup(\n            analysis_type, real_world_conditions, geometry_features\n        )\n        \n        # Update project physics configuration\n        project.physics_status = 'defined'\n        project.physics_config = {\n            'analysis_type': analysis_type,\n            'real_world_conditions': real_world_conditions,\n            'geometry_features': geometry_features,\n            'physics_setup': physics_setup\n        }\n        \n        db.session.commit()\n        \n        return jsonify({\n            'status': 'success',\n            'physics_setup': physics_setup\n        })\n        \n    except Exception as e:\n        logging.error(f\"Physics setup error: {str(e)}\")\n        return jsonify({'error': str(e)}), 500\n\***********('/project/<int:project_id>/results')\ndef results(project_id):\n    \"\"\"Final results and export page\"\"\"\n    project = Project.query.get_or_404(project_id)\n    \n    # Check if all preprocessing steps are completed\n    all_completed = all([\n        project.geometry_status == 'analyzed',\n        project.mesh_status == 'planned', \n        project.materials_status == 'recommended',\n        project.physics_status == 'defined'\n    ])\n    \n    if all_completed:\n        project.status = 'preprocessed'\n        db.session.commit()\n    \n    return render_template('results.html', project=project, all_completed=all_completed)\n\***********('/api/export_preprocessing', methods=['POST'])\ndef export_preprocessing():\n    \"\"\"Export preprocessing configuration\"\"\"\n    data = request.get_json()\n    project_id = data.get('project_id')\n    export_format = data.get('format', 'json')\n    \n    project = Project.query.get_or_404(project_id)\n    \n    export_data = {\n        'project_info': {\n            'name': project.name,\n            'description': project.description,\n            'created_at': project.created_at.isoformat(),\n            'status': project.status\n        },\n        'geometry_config': project.geometry_config,\n        'mesh_config': project.mesh_config,\n        'materials_config': project.materials_config,\n        'physics_config': project.physics_config\n    }\n    \n    return jsonify({\n        'status': 'success',\n        'export_data': export_data,\n        'format': export_format\n    })\n\n# Error handlers\******************(404)\ndef not_found(error):\n    return render_template('base.html', error=\"Page not found\"), 404\n\******************(500)\ndef internal_error(error):\n    db.session.rollback()\n    return render_template('base.html', error=\"Internal server error\"), 500\n", "size_bytes": 13257}, "static/css/style.css": {"content": "/* Ensimu.space Custom Styles */\n/* Built on top of Bootstrap 5 with Replit dark theme */\n\n:root {\n    --ensimu-primary: #0066cc;\n    --ensimu-secondary: #6c757d;\n    --ensimu-success: #198754;\n    --ensimu-warning: #ffc107;\n    --ensimu-danger: #dc3545;\n    --ensimu-info: #0dcaf0;\n}\n\n/* Global Styling */\nbody {\n    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n    line-height: 1.6;\n}\n\n/* Custom navbar styling */\n.navbar-brand {\n    font-weight: 600;\n    font-size: 1.3rem;\n}\n\n/* Card enhancements */\n.card {\n    border-radius: 0.5rem;\n    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\n    transition: box-shadow 0.15s ease-in-out;\n}\n\n.card:hover {\n    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n}\n\n.card-header {\n    background: rgba(var(--bs-primary-rgb), 0.1);\n    border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.2);\n}\n\n/* Material cards specific styling */\n.material-card {\n    transition: all 0.2s ease-in-out;\n    cursor: pointer;\n}\n\n.material-card:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n}\n\n/* Progress bars */\n.progress {\n    background-color: rgba(var(--bs-secondary-rgb), 0.2);\n    border-radius: 0.375rem;\n}\n\n.progress-bar {\n    transition: width 0.6s ease;\n}\n\n/* Buttons */\n.btn {\n    border-radius: 0.375rem;\n    transition: all 0.15s ease-in-out;\n}\n\n.btn:hover {\n    transform: translateY(-1px);\n}\n\n/* File upload styling */\n.file-upload-area {\n    border: 2px dashed var(--bs-border-color);\n    border-radius: 0.5rem;\n    padding: 2rem;\n    text-align: center;\n    transition: all 0.3s ease;\n}\n\n.file-upload-area:hover {\n    border-color: var(--bs-primary);\n    background-color: rgba(var(--bs-primary-rgb), 0.05);\n}\n\n.file-upload-area.dragover {\n    border-color: var(--bs-success);\n    background-color: rgba(var(--bs-success-rgb), 0.1);\n}\n\n/* Timeline styling for results page */\n.timeline {\n    position: relative;\n    padding-left: 30px;\n}\n\n.timeline::before {\n    content: '';\n    position: absolute;\n    left: 15px;\n    top: 0;\n    bottom: 0;\n    width: 2px;\n    background: var(--bs-border-color);\n}\n\n.timeline-item {\n    position: relative;\n    margin-bottom: 20px;\n}\n\n.timeline-marker {\n    position: absolute;\n    left: -22px;\n    top: 5px;\n    width: 12px;\n    height: 12px;\n    border-radius: 50%;\n    border: 2px solid var(--bs-body-bg);\n}\n\n.timeline-content h6 {\n    margin-bottom: 2px;\n    font-size: 0.9rem;\n}\n\n/* Alert styling */\n.alert {\n    border-radius: 0.5rem;\n    border: none;\n}\n\n.alert-info {\n    background-color: rgba(var(--bs-info-rgb), 0.1);\n    color: var(--bs-info);\n}\n\n.alert-success {\n    background-color: rgba(var(--bs-success-rgb), 0.1);\n    color: var(--bs-success);\n}\n\n.alert-warning {\n    background-color: rgba(var(--bs-warning-rgb), 0.1);\n    color: var(--bs-warning);\n}\n\n.alert-danger {\n    background-color: rgba(var(--bs-danger-rgb), 0.1);\n    color: var(--bs-danger);\n}\n\n/* Accordion styling */\n.accordion-button {\n    border-radius: 0.375rem !important;\n}\n\n.accordion-button:not(.collapsed) {\n    background-color: rgba(var(--bs-primary-rgb), 0.1);\n    color: var(--bs-primary);\n}\n\n/* Badge styling */\n.badge {\n    font-size: 0.75em;\n    border-radius: 0.25rem;\n}\n\n/* Spinner customization */\n.spinner-border-sm {\n    width: 1rem;\n    height: 1rem;\n}\n\n/* List group styling */\n.list-group-item {\n    border-radius: 0.375rem !important;\n    margin-bottom: 0.25rem;\n    border: 1px solid var(--bs-border-color) !important;\n}\n\n.list-group-item:hover {\n    background-color: rgba(var(--bs-primary-rgb), 0.05);\n}\n\n/* Form controls */\n.form-control, .form-select {\n    border-radius: 0.375rem;\n    transition: all 0.15s ease-in-out;\n}\n\n.form-control:focus, .form-select:focus {\n    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);\n}\n\n/* Modal styling */\n.modal-content {\n    border-radius: 0.5rem;\n    border: none;\n}\n\n.modal-header {\n    border-bottom: 1px solid rgba(var(--bs-border-color-translucent));\n    border-radius: 0.5rem 0.5rem 0 0;\n}\n\n.modal-footer {\n    border-top: 1px solid rgba(var(--bs-border-color-translucent));\n    border-radius: 0 0 0.5rem 0.5rem;\n}\n\n/* Utility classes */\n.text-ensimu-primary {\n    color: var(--ensimu-primary) !important;\n}\n\n.bg-ensimu-primary {\n    background-color: var(--ensimu-primary) !important;\n}\n\n.border-ensimu-primary {\n    border-color: var(--ensimu-primary) !important;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n    .card-body {\n        padding: 1rem;\n    }\n    \n    .timeline {\n        padding-left: 20px;\n    }\n    \n    .timeline::before {\n        left: 10px;\n    }\n    \n    .timeline-marker {\n        left: -17px;\n    }\n}\n\n/* Animation classes */\n.fade-in {\n    animation: fadeIn 0.5s ease-in;\n}\n\n@keyframes fadeIn {\n    from {\n        opacity: 0;\n        transform: translateY(20px);\n    }\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n.slide-in {\n    animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n    from {\n        transform: translateX(-100%);\n    }\n    to {\n        transform: translateX(0);\n    }\n}\n\n/* Status indicators */\n.status-indicator {\n    display: inline-block;\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    margin-right: 0.5rem;\n}\n\n.status-pending {\n    background-color: var(--bs-secondary);\n}\n\n.status-in-progress {\n    background-color: var(--bs-warning);\n    animation: pulse 2s infinite;\n}\n\n.status-completed {\n    background-color: var(--bs-success);\n}\n\n.status-error {\n    background-color: var(--bs-danger);\n}\n\n@keyframes pulse {\n    0% {\n        opacity: 1;\n    }\n    50% {\n        opacity: 0.5;\n    }\n    100% {\n        opacity: 1;\n    }\n}\n\n/* Loading states */\n.loading-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: rgba(var(--bs-body-bg-rgb), 0.8);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: inherit;\n    z-index: 10;\n}\n\n/* Preprocessing workflow specific styles */\n.preprocessing-step {\n    position: relative;\n    overflow: hidden;\n}\n\n.preprocessing-step.completed::after {\n    content: '✓';\n    position: absolute;\n    top: 0.5rem;\n    right: 0.5rem;\n    color: var(--bs-success);\n    font-weight: bold;\n    font-size: 1.2rem;\n}\n\n.preprocessing-step.in-progress::after {\n    content: '⟳';\n    position: absolute;\n    top: 0.5rem;\n    right: 0.5rem;\n    color: var(--bs-warning);\n    font-weight: bold;\n    font-size: 1.2rem;\n    animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n    from {\n        transform: rotate(0deg);\n    }\n    to {\n        transform: rotate(360deg);\n    }\n}\n\n/* Print styles */\n@media print {\n    .navbar,\n    .btn,\n    .card-header,\n    .modal {\n        display: none !important;\n    }\n    \n    .card {\n        border: 1px solid #000 !important;\n        box-shadow: none !important;\n    }\n    \n    .card-body {\n        padding: 1rem !important;\n    }\n}\n", "size_bytes": 6909}, "static/js/app.js": {"content": "/**\n * Ensimu.space - AI-Powered CAE Preprocessing Platform\n * Main JavaScript Application\n */\n\n// Global application object\nwindow.Ensimu = {\n    // Configuration\n    config: {\n        apiTimeout: 30000,\n        maxFileSize: 100 * 1024 * 1024, // 100MB\n        supportedFormats: ['.step', '.stp', '.iges', '.igs', '.stl', '.obj', '.ply', '.x_t', '.x_b']\n    },\n\n    // Utility functions\n    utils: {\n        /**\n         * Format file size in human readable format\n         */\n        formatFileSize: function(bytes) {\n            if (bytes === 0) return '0 Bytes';\n            const k = 1024;\n            const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n            const i = Math.floor(Math.log(bytes) / Math.log(k));\n            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n        },\n\n        /**\n         * Get file extension\n         */\n        getFileExtension: function(filename) {\n            return '.' + filename.split('.').pop().toLowerCase();\n        },\n\n        /**\n         * Check if file format is supported\n         */\n        isSupportedFormat: function(filename) {\n            const ext = this.getFileExtension(filename);\n            return Ensimu.config.supportedFormats.includes(ext);\n        },\n\n        /**\n         * Get CAD file type from extension\n         */\n        getCADFileType: function(filename) {\n            const ext = this.getFileExtension(filename);\n            const typeMap = {\n                '.step': 'STEP',\n                '.stp': 'STEP',\n                '.iges': 'IGES',\n                '.igs': 'IGES',\n                '.stl': 'STL',\n                '.obj': 'OBJ',\n                '.ply': 'PLY',\n                '.x_t': 'Parasolid',\n                '.x_b': 'Parasolid'\n            };\n            return typeMap[ext] || 'Unknown';\n        },\n\n        /**\n         * Show loading state on button\n         */\n        showButtonLoading: function(button, loadingText = 'Loading...') {\n            if (!button) return;\n            button.dataset.originalText = button.innerHTML;\n            button.innerHTML = `<span class=\"spinner-border spinner-border-sm me-1\"></span>${loadingText}`;\n            button.disabled = true;\n        },\n\n        /**\n         * Hide loading state on button\n         */\n        hideButtonLoading: function(button) {\n            if (!button || !button.dataset.originalText) return;\n            button.innerHTML = button.dataset.originalText;\n            button.disabled = false;\n            delete button.dataset.originalText;\n        },\n\n        /**\n         * Show toast notification\n         */\n        showToast: function(message, type = 'info') {\n            // Create toast container if it doesn't exist\n            let toastContainer = document.getElementById('toast-container');\n            if (!toastContainer) {\n                toastContainer = document.createElement('div');\n                toastContainer.id = 'toast-container';\n                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';\n                toastContainer.style.zIndex = '1100';\n                document.body.appendChild(toastContainer);\n            }\n\n            // Create toast element\n            const toastId = 'toast-' + Date.now();\n            const toastHTML = `\n                <div class=\"toast align-items-center text-bg-${type}\" role=\"alert\" id=\"${toastId}\">\n                    <div class=\"d-flex\">\n                        <div class=\"toast-body\">\n                            ${message}\n                        </div>\n                        <button type=\"button\" class=\"btn-close btn-close-white me-2 m-auto\" data-bs-dismiss=\"toast\"></button>\n                    </div>\n                </div>\n            `;\n\n            toastContainer.insertAdjacentHTML('beforeend', toastHTML);\n            const toastElement = document.getElementById(toastId);\n            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 5000 });\n            toast.show();\n\n            // Remove toast element after it's hidden\n            toastElement.addEventListener('hidden.bs.toast', () => {\n                toastElement.remove();\n            });\n        },\n\n        /**\n         * Make API request with error handling\n         */\n        apiRequest: async function(url, options = {}) {\n            const defaultOptions = {\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                timeout: Ensimu.config.apiTimeout\n            };\n\n            const mergedOptions = { ...defaultOptions, ...options };\n            \n            try {\n                const controller = new AbortController();\n                const timeoutId = setTimeout(() => controller.abort(), mergedOptions.timeout);\n                \n                const response = await fetch(url, {\n                    ...mergedOptions,\n                    signal: controller.signal\n                });\n                \n                clearTimeout(timeoutId);\n                \n                if (!response.ok) {\n                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n                }\n                \n                const contentType = response.headers.get('content-type');\n                if (contentType && contentType.includes('application/json')) {\n                    return await response.json();\n                } else {\n                    return await response.text();\n                }\n            } catch (error) {\n                if (error.name === 'AbortError') {\n                    throw new Error('Request timed out');\n                }\n                throw error;\n            }\n        },\n\n        /**\n         * Debounce function\n         */\n        debounce: function(func, wait) {\n            let timeout;\n            return function executedFunction(...args) {\n                const later = () => {\n                    clearTimeout(timeout);\n                    func(...args);\n                };\n                clearTimeout(timeout);\n                timeout = setTimeout(later, wait);\n            };\n        },\n\n        /**\n         * Animate progress bar\n         */\n        animateProgress: function(progressBar, targetWidth, duration = 1000) {\n            if (!progressBar) return;\n            \n            const startWidth = parseFloat(progressBar.style.width) || 0;\n            const startTime = performance.now();\n            \n            function animate(currentTime) {\n                const elapsed = currentTime - startTime;\n                const progress = Math.min(elapsed / duration, 1);\n                const easeProgress = 1 - Math.pow(1 - progress, 3); // Ease out cubic\n                const currentWidth = startWidth + (targetWidth - startWidth) * easeProgress;\n                \n                progressBar.style.width = currentWidth + '%';\n                \n                if (progress < 1) {\n                    requestAnimationFrame(animate);\n                }\n            }\n            \n            requestAnimationFrame(animate);\n        }\n    },\n\n    // File upload handling\n    fileUpload: {\n        /**\n         * Initialize file upload functionality\n         */\n        init: function() {\n            this.setupDragAndDrop();\n            this.setupFileInput();\n        },\n\n        /**\n         * Setup drag and drop functionality\n         */\n        setupDragAndDrop: function() {\n            const dropZones = document.querySelectorAll('.file-upload-area, [data-drop-zone]');\n            \n            dropZones.forEach(zone => {\n                zone.addEventListener('dragenter', this.handleDragEnter.bind(this));\n                zone.addEventListener('dragover', this.handleDragOver.bind(this));\n                zone.addEventListener('dragleave', this.handleDragLeave.bind(this));\n                zone.addEventListener('drop', this.handleDrop.bind(this));\n            });\n        },\n\n        /**\n         * Setup file input functionality\n         */\n        setupFileInput: function() {\n            const fileInputs = document.querySelectorAll('input[type=\"file\"]');\n            \n            fileInputs.forEach(input => {\n                input.addEventListener('change', this.handleFileSelect.bind(this));\n            });\n        },\n\n        handleDragEnter: function(e) {\n            e.preventDefault();\n            e.stopPropagation();\n            e.currentTarget.classList.add('dragover');\n        },\n\n        handleDragOver: function(e) {\n            e.preventDefault();\n            e.stopPropagation();\n        },\n\n        handleDragLeave: function(e) {\n            e.preventDefault();\n            e.stopPropagation();\n            if (!e.currentTarget.contains(e.relatedTarget)) {\n                e.currentTarget.classList.remove('dragover');\n            }\n        },\n\n        handleDrop: function(e) {\n            e.preventDefault();\n            e.stopPropagation();\n            e.currentTarget.classList.remove('dragover');\n            \n            const files = Array.from(e.dataTransfer.files);\n            this.processFiles(files);\n        },\n\n        handleFileSelect: function(e) {\n            const files = Array.from(e.target.files);\n            this.processFiles(files);\n        },\n\n        /**\n         * Process selected files\n         */\n        processFiles: function(files) {\n            const validFiles = [];\n            const invalidFiles = [];\n            \n            files.forEach(file => {\n                if (this.validateFile(file)) {\n                    validFiles.push(file);\n                } else {\n                    invalidFiles.push(file);\n                }\n            });\n            \n            if (invalidFiles.length > 0) {\n                const invalidNames = invalidFiles.map(f => f.name).join(', ');\n                Ensimu.utils.showToast(`Invalid files: ${invalidNames}`, 'warning');\n            }\n            \n            if (validFiles.length > 0) {\n                this.displayFilePreview(validFiles);\n                Ensimu.utils.showToast(`${validFiles.length} file(s) ready for upload`, 'success');\n            }\n        },\n\n        /**\n         * Validate file\n         */\n        validateFile: function(file) {\n            // Check file size\n            if (file.size > Ensimu.config.maxFileSize) {\n                return false;\n            }\n            \n            // Check file format\n            if (!Ensimu.utils.isSupportedFormat(file.name)) {\n                return false;\n            }\n            \n            return true;\n        },\n\n        /**\n         * Display file preview\n         */\n        displayFilePreview: function(files) {\n            const previewContainer = document.getElementById('filePreview') || document.querySelector('[data-file-preview]');\n            const fileList = document.getElementById('fileList') || previewContainer?.querySelector('[data-file-list]');\n            \n            if (!previewContainer || !fileList) return;\n            \n            previewContainer.classList.remove('d-none');\n            fileList.innerHTML = '';\n            \n            files.forEach((file, index) => {\n                const fileItem = this.createFilePreviewItem(file, index);\n                fileList.appendChild(fileItem);\n            });\n        },\n\n        /**\n         * Create file preview item\n         */\n        createFilePreviewItem: function(file, index) {\n            const div = document.createElement('div');\n            div.className = 'list-group-item d-flex justify-content-between align-items-center';\n            \n            const fileInfo = document.createElement('div');\n            fileInfo.innerHTML = `\n                <strong>${file.name}</strong><br>\n                <small class=\"text-muted\">\n                    ${Ensimu.utils.formatFileSize(file.size)} - ${Ensimu.utils.getCADFileType(file.name)}\n                </small>\n            `;\n            \n            const statusBadge = document.createElement('span');\n            statusBadge.className = 'badge bg-success';\n            statusBadge.textContent = 'Ready';\n            \n            div.appendChild(fileInfo);\n            div.appendChild(statusBadge);\n            \n            return div;\n        }\n    },\n\n    // Application initialization\n    init: function() {\n        // Initialize when DOM is ready\n        if (document.readyState === 'loading') {\n            document.addEventListener('DOMContentLoaded', this.onDOMReady.bind(this));\n        } else {\n            this.onDOMReady();\n        }\n    },\n\n    onDOMReady: function() {\n        console.log('Ensimu.space application initialized');\n        \n        // Initialize components\n        this.fileUpload.init();\n        this.setupGlobalEventListeners();\n        this.initializeTooltips();\n        this.initializeFeatherIcons();\n        \n        // Show welcome message on first visit\n        if (window.location.pathname === '/' && !localStorage.getItem('ensimu_visited')) {\n            this.showWelcomeMessage();\n            localStorage.setItem('ensimu_visited', 'true');\n        }\n    },\n\n    /**\n     * Setup global event listeners\n     */\n    setupGlobalEventListeners: function() {\n        // Handle form submissions with loading states\n        document.addEventListener('submit', (e) => {\n            const submitBtn = e.target.querySelector('button[type=\"submit\"]');\n            if (submitBtn && !submitBtn.classList.contains('no-loading')) {\n                Ensimu.utils.showButtonLoading(submitBtn, 'Processing...');\n            }\n        });\n\n        // Handle navigation clicks\n        document.addEventListener('click', (e) => {\n            if (e.target.matches('.nav-link, .navbar-brand')) {\n                this.handleNavigation(e);\n            }\n        });\n\n        // Handle escape key to close modals\n        document.addEventListener('keydown', (e) => {\n            if (e.key === 'Escape') {\n                const openModals = document.querySelectorAll('.modal.show');\n                openModals.forEach(modal => {\n                    bootstrap.Modal.getInstance(modal)?.hide();\n                });\n            }\n        });\n    },\n\n    /**\n     * Initialize tooltips\n     */\n    initializeTooltips: function() {\n        const tooltips = document.querySelectorAll('[data-bs-toggle=\"tooltip\"]');\n        tooltips.forEach(tooltip => {\n            new bootstrap.Tooltip(tooltip);\n        });\n    },\n\n    /**\n     * Initialize Feather icons\n     */\n    initializeFeatherIcons: function() {\n        if (typeof feather !== 'undefined') {\n            feather.replace();\n        }\n    },\n\n    /**\n     * Handle navigation\n     */\n    handleNavigation: function(e) {\n        const link = e.target.closest('a');\n        if (link && link.href && !link.target) {\n            // Add loading indication for internal navigation\n            Ensimu.utils.showButtonLoading(link, 'Loading...');\n        }\n    },\n\n    /**\n     * Show welcome message\n     */\n    showWelcomeMessage: function() {\n        setTimeout(() => {\n            Ensimu.utils.showToast(\n                'Welcome to Ensimu.space! Start by creating a new CAE preprocessing project.',\n                'info'\n            );\n        }, 1000);\n    }\n};\n\n// Initialize the application\nEnsimu.init();\n\n// Export for global access\nwindow.Ensimu = Ensimu;\n", "size_bytes": 15261}, "static/js/preprocessing.js": {"content": "/**\n * Ensimu.space - Preprocessing Workflow JavaScript\n * Handles AI agent interactions and preprocessing workflow\n */\n\nwindow.PreprocessingWorkflow = {\n    // Current project and state\n    currentProject: null,\n    currentStep: null,\n    aiAgents: {\n        geometry: null,\n        mesh: null,\n        materials: null,\n        physics: null\n    },\n\n    /**\n     * Initialize preprocessing workflow\n     */\n    init: function(projectId) {\n        this.currentProject = projectId;\n        this.setupEventListeners();\n        this.initializeProgressTracking();\n        this.loadProjectState();\n        console.log('Preprocessing workflow initialized for project:', projectId);\n    },\n\n    /**\n     * Setup event listeners\n     */\n    setupEventListeners: function() {\n        // AI analysis buttons\n        document.addEventListener('click', (e) => {\n            if (e.target.matches('#analyzeBtn, .analyze-btn')) {\n                this.handleGeometryAnalysis(e);\n            } else if (e.target.matches('#recommendBtn, .recommend-btn')) {\n                this.handleMeshRecommendation(e);\n            } else if (e.target.matches('#materialRecommendBtn, .material-recommend-btn')) {\n                this.handleMaterialRecommendation(e);\n            } else if (e.target.matches('#defineBtn, .define-btn')) {\n                this.handlePhysicsDefinition(e);\n            }\n        });\n\n        // Progress tracking\n        document.addEventListener('change', (e) => {\n            if (e.target.matches('input[type=\"checkbox\"][id^=\"check_\"]')) {\n                this.updateProgressTracking();\n            }\n        });\n\n        // Auto-save form data\n        document.addEventListener('input', this.debounce((e) => {\n            if (e.target.matches('textarea, input[type=\"text\"], select')) {\n                this.autoSaveFormData(e.target);\n            }\n        }, 1000));\n    },\n\n    /**\n     * Initialize progress tracking\n     */\n    initializeProgressTracking: function() {\n        this.updateOverallProgress();\n        \n        // Setup progress indicators\n        const progressBars = document.querySelectorAll('.progress-bar[data-step]');\n        progressBars.forEach(bar => {\n            const step = bar.dataset.step;\n            this.updateStepProgress(step);\n        });\n    },\n\n    /**\n     * Load project state from server\n     */\n    loadProjectState: async function() {\n        try {\n            const response = await fetch(`/api/project/${this.currentProject}/state`);\n            if (response.ok) {\n                const state = await response.json();\n                this.restoreFormState(state);\n            }\n        } catch (error) {\n            console.warn('Could not load project state:', error);\n        }\n    },\n\n    /**\n     * Handle geometry analysis\n     */\n    handleGeometryAnalysis: async function(e) {\n        e.preventDefault();\n        const form = e.target.closest('form');\n        const formData = new FormData(form);\n        \n        const analysisData = {\n            project_id: this.currentProject,\n            analysis_objectives: formData.get('analysis_objectives'),\n            defeaturing_level: formData.get('defeaturing_level') || 'moderate'\n        };\n\n        await this.runAIAnalysis('geometry', analysisData, {\n            progressElement: '#aiProgress',\n            statusElement: '#agentStatus',\n            resultsContainer: '#analysisResults',\n            resultProcessor: this.displayGeometryResults.bind(this)\n        });\n    },\n\n    /**\n     * Handle mesh recommendation\n     */\n    handleMeshRecommendation: async function(e) {\n        e.preventDefault();\n        const form = e.target.closest('form');\n        const formData = new FormData(form);\n        \n        const meshData = {\n            project_id: this.currentProject,\n            physics_type: formData.get('physics_type'),\n            computational_resources: formData.get('computational_resources'),\n            accuracy_requirements: formData.get('accuracy_requirements'),\n            mesh_complexity: formData.get('mesh_complexity'),\n            critical_regions: formData.get('critical_regions')\n        };\n\n        await this.runAIAnalysis('mesh', meshData, {\n            progressElement: '#meshProgress',\n            statusElement: '#meshAgentStatus',\n            resultsContainer: '#meshRecommendations',\n            resultProcessor: this.displayMeshResults.bind(this)\n        });\n    },\n\n    /**\n     * Handle material recommendation\n     */\n    handleMaterialRecommendation: async function(e) {\n        e.preventDefault();\n        const form = e.target.closest('form');\n        const formData = new FormData(form);\n        \n        const materialData = {\n            project_id: this.currentProject,\n            component_info: formData.get('component_info'),\n            operating_conditions: `Temperature: ${document.getElementById('temp_min')?.value || 20}°C to ${document.getElementById('temp_max')?.value || 80}°C, Environment: ${formData.get('environmental_conditions')}`,\n            safety_requirements: `Safety factor: ${formData.get('safety_factor')}, Cost priority: ${formData.get('cost_priority')}`\n        };\n\n        await this.runAIAnalysis('materials', materialData, {\n            progressElement: '#materialProgress',\n            statusElement: '#materialAgentStatus',\n            resultsContainer: '#materialRecommendations',\n            resultProcessor: this.displayMaterialResults.bind(this)\n        });\n    },\n\n    /**\n     * Handle physics definition\n     */\n    handlePhysicsDefinition: async function(e) {\n        e.preventDefault();\n        const form = e.target.closest('form');\n        const formData = new FormData(form);\n        \n        const physicsData = {\n            project_id: this.currentProject,\n            analysis_type: formData.get('analysis_type'),\n            real_world_conditions: formData.get('real_world_conditions'),\n            geometry_features: formData.get('geometry_features'),\n            nonlinearity: formData.get('nonlinearity')\n        };\n\n        await this.runAIAnalysis('physics', physicsData, {\n            progressElement: '#physicsProgress',\n            statusElement: '#physicsAgentStatus',\n            resultsContainer: '#physicsRecommendations',\n            resultProcessor: this.displayPhysicsResults.bind(this)\n        });\n    },\n\n    /**\n     * Run AI analysis with unified handling\n     */\n    runAIAnalysis: async function(agentType, data, options) {\n        const {\n            progressElement,\n            statusElement,\n            resultsContainer,\n            resultProcessor\n        } = options;\n\n        const progressBar = document.querySelector(progressElement);\n        const statusText = document.querySelector(statusElement);\n        const resultsDiv = document.querySelector(resultsContainer);\n        const resultsContent = resultsDiv?.querySelector('[id$=\"Content\"]');\n\n        // Update UI to show analysis in progress\n        if (statusText) statusText.textContent = `Analyzing ${agentType} requirements...`;\n        \n        // Animate progress\n        let progress = 0;\n        const progressInterval = setInterval(() => {\n            progress += Math.random() * 15;\n            if (progress > 85) progress = 85;\n            if (progressBar) {\n                Ensimu.utils.animateProgress(progressBar, progress);\n            }\n        }, 600);\n\n        try {\n            const apiEndpoint = {\n                'geometry': '/api/analyze_geometry',\n                'mesh': '/api/recommend_mesh',\n                'materials': '/api/recommend_materials',\n                'physics': '/api/define_physics'\n            }[agentType];\n\n            const response = await Ensimu.utils.apiRequest(apiEndpoint, {\n                method: 'POST',\n                body: JSON.stringify(data)\n            });\n\n            clearInterval(progressInterval);\n            if (progressBar) {\n                Ensimu.utils.animateProgress(progressBar, 100);\n            }\n\n            if (response.status === 'success') {\n                if (resultProcessor) {\n                    resultProcessor(response);\n                }\n                if (statusText) statusText.textContent = `${agentType} analysis complete`;\n                this.updateStepProgress(agentType);\n                Ensimu.utils.showToast(`${agentType} analysis completed successfully`, 'success');\n            } else {\n                throw new Error(response.error || `${agentType} analysis failed`);\n            }\n\n        } catch (error) {\n            clearInterval(progressInterval);\n            if (progressBar) {\n                Ensimu.utils.animateProgress(progressBar, 0);\n            }\n            if (statusText) statusText.textContent = `${agentType} analysis failed`;\n\n            const errorMessage = `\n                <div class=\"alert alert-danger\">\n                    <i data-feather=\"alert-triangle\" class=\"me-2\"></i>\n                    ${agentType} analysis failed: ${error.message}\n                </div>\n            `;\n            \n            if (resultsContent) {\n                resultsContent.innerHTML = errorMessage;\n                resultsDiv?.classList.remove('d-none');\n                feather.replace();\n            }\n\n            Ensimu.utils.showToast(`${agentType} analysis failed: ${error.message}`, 'danger');\n        }\n    },\n\n    /**\n     * Display geometry analysis results\n     */\n    displayGeometryResults: function(response) {\n        const resultsDiv = document.getElementById('analysisResults');\n        const resultsContent = document.getElementById('resultsContent');\n        \n        if (!resultsDiv || !resultsContent) return;\n\n        let html = '<div class=\"row g-3\">';\n        \n        response.results.forEach((result) => {\n            const analysis = result.analysis;\n            \n            html += `\n                <div class=\"col-12\">\n                    <div class=\"card\">\n                        <div class=\"card-header\">\n                            <h6 class=\"mb-0\">${result.filename}</h6>\n                        </div>\n                        <div class=\"card-body\">\n            `;\n            \n            if (analysis.error) {\n                html += `\n                    <div class=\"alert alert-danger\">\n                        <i data-feather=\"alert-triangle\" class=\"me-2\"></i>\n                        Error: ${analysis.error}\n                    </div>\n                `;\n            } else {\n                // Display recommendations\n                if (analysis.recommendations) {\n                    html += `\n                        <div class=\"mb-3\">\n                            <h6><i data-feather=\"lightbulb\" class=\"me-1\"></i>Recommendations:</h6>\n                            <ul class=\"small\">\n                    `;\n                    analysis.recommendations.forEach(rec => {\n                        html += `<li>${rec}</li>`;\n                    });\n                    html += '</ul></div>';\n                }\n                \n                // Display defeaturing steps\n                if (analysis.defeaturing_steps) {\n                    html += `\n                        <div class=\"mb-3\">\n                            <h6><i data-feather=\"tool\" class=\"me-1\"></i>Defeaturing Steps:</h6>\n                            <ol class=\"small\">\n                    `;\n                    analysis.defeaturing_steps.forEach(step => {\n                        html += `<li>${step}</li>`;\n                    });\n                    html += '</ol></div>';\n                }\n                \n                // Display confidence score\n                if (analysis.confidence_score) {\n                    const confidence = parseFloat(analysis.confidence_score) * 100;\n                    const confidenceColor = confidence > 80 ? 'success' : confidence > 60 ? 'warning' : 'danger';\n                    html += `\n                        <div class=\"progress mb-2\" style=\"height: 20px;\">\n                            <div class=\"progress-bar bg-${confidenceColor}\" style=\"width: ${confidence}%\">\n                                Confidence: ${confidence.toFixed(0)}%\n                            </div>\n                        </div>\n                    `;\n                }\n            }\n            \n            html += '</div></div></div>';\n        });\n        \n        html += '</div>';\n        resultsContent.innerHTML = html;\n        resultsDiv.classList.remove('d-none');\n        feather.replace();\n\n        // Show next step button\n        const nextBtn = document.getElementById('nextBtn');\n        if (nextBtn) nextBtn.style.display = 'inline-block';\n    },\n\n    /**\n     * Display mesh analysis results\n     */\n    displayMeshResults: function(response) {\n        const resultsDiv = document.getElementById('meshRecommendations');\n        const resultsContent = document.getElementById('recommendationsContent');\n        \n        if (!resultsDiv || !resultsContent) return;\n\n        const recommendations = response.recommendations;\n        let html = '<div class=\"row g-3\">';\n        \n        // Mesh Strategy\n        if (recommendations.mesh_strategy) {\n            html += `\n                <div class=\"col-md-6\">\n                    <div class=\"card border-success\">\n                        <div class=\"card-body\">\n                            <h6><i data-feather=\"grid\" class=\"me-1\"></i>Recommended Strategy</h6>\n                            <p class=\"small\">${recommendations.mesh_strategy}</p>\n                        </div>\n                    </div>\n                </div>\n            `;\n        }\n        \n        // Element Types\n        if (recommendations.element_types) {\n            html += `\n                <div class=\"col-md-6\">\n                    <div class=\"card border-info\">\n                        <div class=\"card-body\">\n                            <h6><i data-feather=\"box\" class=\"me-1\"></i>Element Types</h6>\n                            <ul class=\"small mb-0\">\n            `;\n            recommendations.element_types.forEach(type => {\n                html += `<li>${type}</li>`;\n            });\n            html += '</ul></div></div></div>';\n        }\n        \n        html += '</div>';\n        resultsContent.innerHTML = html;\n        resultsDiv.classList.remove('d-none');\n        feather.replace();\n\n        // Show next step button\n        const nextBtn = document.getElementById('nextBtn');\n        if (nextBtn) nextBtn.style.display = 'inline-block';\n    },\n\n    /**\n     * Display material analysis results\n     */\n    displayMaterialResults: function(response) {\n        const resultsDiv = document.getElementById('materialRecommendations');\n        const resultsContent = document.getElementById('recommendationsContent');\n        \n        if (!resultsDiv || !resultsContent) return;\n\n        const recommendations = response.recommendations;\n        let html = '<div class=\"row g-3\">';\n        \n        // Material Recommendations\n        if (recommendations.material_recommendations) {\n            html += `\n                <div class=\"col-12\">\n                    <div class=\"card border-warning\">\n                        <div class=\"card-body\">\n                            <h6><i data-feather=\"award\" class=\"me-1\"></i>Recommended Materials</h6>\n                            <ul class=\"mb-0\">\n            `;\n            recommendations.material_recommendations.forEach(rec => {\n                html += `<li>${rec}</li>`;\n            });\n            html += '</ul></div></div></div>';\n        }\n        \n        html += '</div>';\n        resultsContent.innerHTML = html;\n        resultsDiv.classList.remove('d-none');\n        feather.replace();\n\n        // Show next step button\n        const nextBtn = document.getElementById('nextBtn');\n        if (nextBtn) nextBtn.style.display = 'inline-block';\n    },\n\n    /**\n     * Display physics analysis results\n     */\n    displayPhysicsResults: function(response) {\n        const resultsDiv = document.getElementById('physicsRecommendations');\n        const resultsContent = document.getElementById('physicsContent');\n        \n        if (!resultsDiv || !resultsContent) return;\n\n        const physicsSetup = response.physics_setup;\n        let html = '<div class=\"row g-3\">';\n        \n        // Boundary Conditions\n        if (physicsSetup.boundary_conditions) {\n            html += `\n                <div class=\"col-md-6\">\n                    <div class=\"card border-info\">\n                        <div class=\"card-body\">\n                            <h6><i data-feather=\"anchor\" class=\"me-1\"></i>Boundary Conditions</h6>\n                            <ul class=\"small mb-0\">\n            `;\n            physicsSetup.boundary_conditions.forEach(bc => {\n                html += `<li>${bc}</li>`;\n            });\n            html += '</ul></div></div></div>';\n        }\n        \n        html += '</div>';\n        resultsContent.innerHTML = html;\n        resultsDiv.classList.remove('d-none');\n        feather.replace();\n\n        // Show next step button\n        const nextBtn = document.getElementById('nextBtn');\n        if (nextBtn) nextBtn.style.display = 'inline-block';\n    },\n\n    /**\n     * Update step progress\n     */\n    updateStepProgress: function(step) {\n        const stepIndicators = document.querySelectorAll(`[data-step=\"${step}\"]`);\n        stepIndicators.forEach(indicator => {\n            indicator.classList.add('completed');\n            indicator.classList.remove('pending', 'in-progress');\n        });\n        \n        this.updateOverallProgress();\n    },\n\n    /**\n     * Update overall progress\n     */\n    updateOverallProgress: function() {\n        const totalSteps = 4; // geometry, mesh, materials, physics\n        const completedSteps = document.querySelectorAll('.preprocessing-step.completed').length;\n        const progress = (completedSteps / totalSteps) * 100;\n        \n        const overallProgress = document.querySelector('.progress-bar[data-overall]');\n        if (overallProgress) {\n            Ensimu.utils.animateProgress(overallProgress, progress);\n        }\n        \n        // Update progress text\n        const progressText = document.querySelector('[data-progress-text]');\n        if (progressText) {\n            progressText.textContent = `${Math.round(progress)}% Complete`;\n        }\n    },\n\n    /**\n     * Update progress tracking from checklist\n     */\n    updateProgressTracking: function() {\n        const checkboxes = document.querySelectorAll('input[type=\"checkbox\"][id^=\"check_\"]');\n        const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;\n        const totalCount = checkboxes.length;\n        \n        if (totalCount > 0) {\n            const percentage = (checkedCount / totalCount) * 100;\n            const progressBar = document.querySelector('.progress-bar[data-checklist]');\n            if (progressBar) {\n                Ensimu.utils.animateProgress(progressBar, percentage);\n            }\n        }\n    },\n\n    /**\n     * Auto-save form data\n     */\n    autoSaveFormData: function(element) {\n        const formData = {\n            elementId: element.id,\n            value: element.value,\n            timestamp: Date.now()\n        };\n        \n        const storageKey = `ensimu_form_${this.currentProject}`;\n        const existingData = JSON.parse(localStorage.getItem(storageKey) || '{}');\n        existingData[element.id] = formData;\n        \n        localStorage.setItem(storageKey, JSON.stringify(existingData));\n    },\n\n    /**\n     * Restore form state\n     */\n    restoreFormState: function(state) {\n        const storageKey = `ensimu_form_${this.currentProject}`;\n        const savedData = JSON.parse(localStorage.getItem(storageKey) || '{}');\n        \n        Object.keys(savedData).forEach(elementId => {\n            const element = document.getElementById(elementId);\n            if (element && savedData[elementId].value) {\n                element.value = savedData[elementId].value;\n            }\n        });\n    },\n\n    /**\n     * Debounce utility\n     */\n    debounce: function(func, wait) {\n        let timeout;\n        return function executedFunction(...args) {\n            const later = () => {\n                clearTimeout(timeout);\n                func(...args);\n            };\n            clearTimeout(timeout);\n            timeout = setTimeout(later, wait);\n        };\n    }\n};\n\n// Initialize when DOM is ready\ndocument.addEventListener('DOMContentLoaded', function() {\n    // Auto-initialize if project ID is available\n    const projectMeta = document.querySelector('meta[name=\"project-id\"]');\n    if (projectMeta) {\n        const projectId = projectMeta.getAttribute('content');\n        PreprocessingWorkflow.init(parseInt(projectId));\n    }\n});\n\n// Export for global access\nwindow.PreprocessingWorkflow = PreprocessingWorkflow;\n", "size_bytes": 20769}}}