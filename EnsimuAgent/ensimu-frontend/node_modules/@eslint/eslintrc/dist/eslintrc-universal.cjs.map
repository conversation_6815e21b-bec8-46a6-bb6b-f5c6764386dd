{"version": 3, "file": "eslintrc-universal.cjs", "sources": ["../lib/shared/config-ops.js", "../lib/shared/deprecation-warnings.js", "../lib/shared/ajv.js", "../conf/config-schema.js", "../conf/environments.js", "../lib/shared/config-validator.js", "../lib/shared/naming.js", "../lib/index-universal.js"], "sourcesContent": ["/**\n * @fileoverview Config file operations. This file must be usable in the browser,\n * so no Node-specific code can be here.\n * <AUTHOR>\n */\n\n//------------------------------------------------------------------------------\n// Private\n//------------------------------------------------------------------------------\n\nconst RULE_SEVERITY_STRINGS = [\"off\", \"warn\", \"error\"],\n    RULE_SEVERITY = RULE_SEVERITY_STRINGS.reduce((map, value, index) => {\n        map[value] = index;\n        return map;\n    }, {}),\n    VALID_SEVERITIES = [0, 1, 2, \"off\", \"warn\", \"error\"];\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\n/**\n * Normalizes the severity value of a rule's configuration to a number\n * @param {(number|string|[number, ...*]|[string, ...*])} ruleConfig A rule's configuration value, generally\n * received from the user. A valid config value is either 0, 1, 2, the string \"off\" (treated the same as 0),\n * the string \"warn\" (treated the same as 1), the string \"error\" (treated the same as 2), or an array\n * whose first element is one of the above values. Strings are matched case-insensitively.\n * @returns {(0|1|2)} The numeric severity value if the config value was valid, otherwise 0.\n */\nfunction getRuleSeverity(ruleConfig) {\n    const severityValue = Array.isArray(ruleConfig) ? ruleConfig[0] : ruleConfig;\n\n    if (severityValue === 0 || severityValue === 1 || severityValue === 2) {\n        return severityValue;\n    }\n\n    if (typeof severityValue === \"string\") {\n        return RULE_SEVERITY[severityValue.toLowerCase()] || 0;\n    }\n\n    return 0;\n}\n\n/**\n * Converts old-style severity settings (0, 1, 2) into new-style\n * severity settings (off, warn, error) for all rules. Assumption is that severity\n * values have already been validated as correct.\n * @param {Object} config The config object to normalize.\n * @returns {void}\n */\nfunction normalizeToStrings(config) {\n\n    if (config.rules) {\n        Object.keys(config.rules).forEach(ruleId => {\n            const ruleConfig = config.rules[ruleId];\n\n            if (typeof ruleConfig === \"number\") {\n                config.rules[ruleId] = RULE_SEVERITY_STRINGS[ruleConfig] || RULE_SEVERITY_STRINGS[0];\n            } else if (Array.isArray(ruleConfig) && typeof ruleConfig[0] === \"number\") {\n                ruleConfig[0] = RULE_SEVERITY_STRINGS[ruleConfig[0]] || RULE_SEVERITY_STRINGS[0];\n            }\n        });\n    }\n}\n\n/**\n * Determines if the severity for the given rule configuration represents an error.\n * @param {int|string|Array} ruleConfig The configuration for an individual rule.\n * @returns {boolean} True if the rule represents an error, false if not.\n */\nfunction isErrorSeverity(ruleConfig) {\n    return getRuleSeverity(ruleConfig) === 2;\n}\n\n/**\n * Checks whether a given config has valid severity or not.\n * @param {number|string|Array} ruleConfig The configuration for an individual rule.\n * @returns {boolean} `true` if the configuration has valid severity.\n */\nfunction isValidSeverity(ruleConfig) {\n    let severity = Array.isArray(ruleConfig) ? ruleConfig[0] : ruleConfig;\n\n    if (typeof severity === \"string\") {\n        severity = severity.toLowerCase();\n    }\n    return VALID_SEVERITIES.indexOf(severity) !== -1;\n}\n\n/**\n * Checks whether every rule of a given config has valid severity or not.\n * @param {Object} config The configuration for rules.\n * @returns {boolean} `true` if the configuration has valid severity.\n */\nfunction isEverySeverityValid(config) {\n    return Object.keys(config).every(ruleId => isValidSeverity(config[ruleId]));\n}\n\n/**\n * Normalizes a value for a global in a config\n * @param {(boolean|string|null)} configuredValue The value given for a global in configuration or in\n * a global directive comment\n * @returns {(\"readable\"|\"writeable\"|\"off\")} The value normalized as a string\n * @throws Error if global value is invalid\n */\nfunction normalizeConfigGlobal(configuredValue) {\n    switch (configuredValue) {\n        case \"off\":\n            return \"off\";\n\n        case true:\n        case \"true\":\n        case \"writeable\":\n        case \"writable\":\n            return \"writable\";\n\n        case null:\n        case false:\n        case \"false\":\n        case \"readable\":\n        case \"readonly\":\n            return \"readonly\";\n\n        default:\n            throw new Error(`'${configuredValue}' is not a valid configuration for a global (use 'readonly', 'writable', or 'off')`);\n    }\n}\n\nexport {\n    getRuleSeverity,\n    normalizeToStrings,\n    isErrorSeverity,\n    isValidSeverity,\n    isEverySeverityValid,\n    normalizeConfigGlobal\n};\n", "/**\n * @fileoverview Provide the function that emits deprecation warnings.\n * <AUTHOR> <http://github.com/mysticatea>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport path from \"path\";\n\n//------------------------------------------------------------------------------\n// Private\n//------------------------------------------------------------------------------\n\n// Defitions for deprecation warnings.\nconst deprecationWarningMessages = {\n    ESLINT_LEGACY_ECMAFEATURES:\n        \"The 'ecmaFeatures' config file property is deprecated and has no effect.\",\n    ESLINT_PERSONAL_CONFIG_LOAD:\n        \"'~/.eslintrc.*' config files have been deprecated. \" +\n        \"Please use a config file per project or the '--config' option.\",\n    ESLINT_PERSONAL_CONFIG_SUPPRESS:\n        \"'~/.eslintrc.*' config files have been deprecated. \" +\n        \"Please remove it or add 'root:true' to the config files in your \" +\n        \"projects in order to avoid loading '~/.eslintrc.*' accidentally.\"\n};\n\nconst sourceFileErrorCache = new Set();\n\n/**\n * Emits a deprecation warning containing a given filepath. A new deprecation warning is emitted\n * for each unique file path, but repeated invocations with the same file path have no effect.\n * No warnings are emitted if the `--no-deprecation` or `--no-warnings` Node runtime flags are active.\n * @param {string} source The name of the configuration source to report the warning for.\n * @param {string} errorCode The warning message to show.\n * @returns {void}\n */\nfunction emitDeprecationWarning(source, errorCode) {\n    const cacheKey = JSON.stringify({ source, errorCode });\n\n    if (sourceFileErrorCache.has(cacheKey)) {\n        return;\n    }\n    sourceFileErrorCache.add(cacheKey);\n\n    const rel = path.relative(process.cwd(), source);\n    const message = deprecationWarningMessages[errorCode];\n\n    process.emitWarning(\n        `${message} (found in \"${rel}\")`,\n        \"DeprecationWarning\",\n        errorCode\n    );\n}\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\nexport {\n    emitDeprecationWarning\n};\n", "/**\n * @fileoverview The instance of Ajv validator.\n * <AUTHOR> <PERSON>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport Ajv from \"ajv\";\n\n//-----------------------------------------------------------------------------\n// Helpers\n//-----------------------------------------------------------------------------\n\n/*\n * Copied from ajv/lib/refs/json-schema-draft-04.json\n * The MIT License (MIT)\n * Copyright (c) 2015-2017 Evgeny Poberezkin\n */\nconst metaSchema = {\n    id: \"http://json-schema.org/draft-04/schema#\",\n    $schema: \"http://json-schema.org/draft-04/schema#\",\n    description: \"Core schema meta-schema\",\n    definitions: {\n        schemaArray: {\n            type: \"array\",\n            minItems: 1,\n            items: { $ref: \"#\" }\n        },\n        positiveInteger: {\n            type: \"integer\",\n            minimum: 0\n        },\n        positiveIntegerDefault0: {\n            allOf: [{ $ref: \"#/definitions/positiveInteger\" }, { default: 0 }]\n        },\n        simpleTypes: {\n            enum: [\"array\", \"boolean\", \"integer\", \"null\", \"number\", \"object\", \"string\"]\n        },\n        stringArray: {\n            type: \"array\",\n            items: { type: \"string\" },\n            minItems: 1,\n            uniqueItems: true\n        }\n    },\n    type: \"object\",\n    properties: {\n        id: {\n            type: \"string\"\n        },\n        $schema: {\n            type: \"string\"\n        },\n        title: {\n            type: \"string\"\n        },\n        description: {\n            type: \"string\"\n        },\n        default: { },\n        multipleOf: {\n            type: \"number\",\n            minimum: 0,\n            exclusiveMinimum: true\n        },\n        maximum: {\n            type: \"number\"\n        },\n        exclusiveMaximum: {\n            type: \"boolean\",\n            default: false\n        },\n        minimum: {\n            type: \"number\"\n        },\n        exclusiveMinimum: {\n            type: \"boolean\",\n            default: false\n        },\n        maxLength: { $ref: \"#/definitions/positiveInteger\" },\n        minLength: { $ref: \"#/definitions/positiveIntegerDefault0\" },\n        pattern: {\n            type: \"string\",\n            format: \"regex\"\n        },\n        additionalItems: {\n            anyOf: [\n                { type: \"boolean\" },\n                { $ref: \"#\" }\n            ],\n            default: { }\n        },\n        items: {\n            anyOf: [\n                { $ref: \"#\" },\n                { $ref: \"#/definitions/schemaArray\" }\n            ],\n            default: { }\n        },\n        maxItems: { $ref: \"#/definitions/positiveInteger\" },\n        minItems: { $ref: \"#/definitions/positiveIntegerDefault0\" },\n        uniqueItems: {\n            type: \"boolean\",\n            default: false\n        },\n        maxProperties: { $ref: \"#/definitions/positiveInteger\" },\n        minProperties: { $ref: \"#/definitions/positiveIntegerDefault0\" },\n        required: { $ref: \"#/definitions/stringArray\" },\n        additionalProperties: {\n            anyOf: [\n                { type: \"boolean\" },\n                { $ref: \"#\" }\n            ],\n            default: { }\n        },\n        definitions: {\n            type: \"object\",\n            additionalProperties: { $ref: \"#\" },\n            default: { }\n        },\n        properties: {\n            type: \"object\",\n            additionalProperties: { $ref: \"#\" },\n            default: { }\n        },\n        patternProperties: {\n            type: \"object\",\n            additionalProperties: { $ref: \"#\" },\n            default: { }\n        },\n        dependencies: {\n            type: \"object\",\n            additionalProperties: {\n                anyOf: [\n                    { $ref: \"#\" },\n                    { $ref: \"#/definitions/stringArray\" }\n                ]\n            }\n        },\n        enum: {\n            type: \"array\",\n            minItems: 1,\n            uniqueItems: true\n        },\n        type: {\n            anyOf: [\n                { $ref: \"#/definitions/simpleTypes\" },\n                {\n                    type: \"array\",\n                    items: { $ref: \"#/definitions/simpleTypes\" },\n                    minItems: 1,\n                    uniqueItems: true\n                }\n            ]\n        },\n        format: { type: \"string\" },\n        allOf: { $ref: \"#/definitions/schemaArray\" },\n        anyOf: { $ref: \"#/definitions/schemaArray\" },\n        oneOf: { $ref: \"#/definitions/schemaArray\" },\n        not: { $ref: \"#\" }\n    },\n    dependencies: {\n        exclusiveMaximum: [\"maximum\"],\n        exclusiveMinimum: [\"minimum\"]\n    },\n    default: { }\n};\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\nexport default (additionalOptions = {}) => {\n    const ajv = new Ajv({\n        meta: false,\n        useDefaults: true,\n        validateSchema: false,\n        missingRefs: \"ignore\",\n        verbose: true,\n        schemaId: \"auto\",\n        ...additionalOptions\n    });\n\n    ajv.addMetaSchema(metaSchema);\n    // eslint-disable-next-line no-underscore-dangle\n    ajv._opts.defaultMeta = metaSchema.id;\n\n    return ajv;\n};\n", "/**\n * @fileoverview Defines a schema for configs.\n * <AUTHOR>\n */\n\nconst baseConfigProperties = {\n    $schema: { type: \"string\" },\n    env: { type: \"object\" },\n    extends: { $ref: \"#/definitions/stringOrStrings\" },\n    globals: { type: \"object\" },\n    overrides: {\n        type: \"array\",\n        items: { $ref: \"#/definitions/overrideConfig\" },\n        additionalItems: false\n    },\n    parser: { type: [\"string\", \"null\"] },\n    parserOptions: { type: \"object\" },\n    plugins: { type: \"array\" },\n    processor: { type: \"string\" },\n    rules: { type: \"object\" },\n    settings: { type: \"object\" },\n    noInlineConfig: { type: \"boolean\" },\n    reportUnusedDisableDirectives: { type: \"boolean\" },\n\n    ecmaFeatures: { type: \"object\" } // deprecated; logs a warning when used\n};\n\nconst configSchema = {\n    definitions: {\n        stringOrStrings: {\n            oneOf: [\n                { type: \"string\" },\n                {\n                    type: \"array\",\n                    items: { type: \"string\" },\n                    additionalItems: false\n                }\n            ]\n        },\n        stringOrStringsRequired: {\n            oneOf: [\n                { type: \"string\" },\n                {\n                    type: \"array\",\n                    items: { type: \"string\" },\n                    additionalItems: false,\n                    minItems: 1\n                }\n            ]\n        },\n\n        // Config at top-level.\n        objectConfig: {\n            type: \"object\",\n            properties: {\n                root: { type: \"boolean\" },\n                ignorePatterns: { $ref: \"#/definitions/stringOrStrings\" },\n                ...baseConfigProperties\n            },\n            additionalProperties: false\n        },\n\n        // Config in `overrides`.\n        overrideConfig: {\n            type: \"object\",\n            properties: {\n                excludedFiles: { $ref: \"#/definitions/stringOrStrings\" },\n                files: { $ref: \"#/definitions/stringOrStringsRequired\" },\n                ...baseConfigProperties\n            },\n            required: [\"files\"],\n            additionalProperties: false\n        }\n    },\n\n    $ref: \"#/definitions/objectConfig\"\n};\n\nexport default configSchema;\n", "/**\n * @fileoverview Defines environment settings and globals.\n * <AUTHOR>\n */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport globals from \"globals\";\n\n//------------------------------------------------------------------------------\n// Helpers\n//------------------------------------------------------------------------------\n\n/**\n * Get the object that has difference.\n * @param {Record<string,boolean>} current The newer object.\n * @param {Record<string,boolean>} prev The older object.\n * @returns {Record<string,boolean>} The difference object.\n */\nfunction getDiff(current, prev) {\n    const retv = {};\n\n    for (const [key, value] of Object.entries(current)) {\n        if (!Object.hasOwnProperty.call(prev, key)) {\n            retv[key] = value;\n        }\n    }\n\n    return retv;\n}\n\nconst newGlobals2015 = getDiff(globals.es2015, globals.es5); // 19 variables such as Promise, Map, ...\nconst newGlobals2017 = {\n    Atomics: false,\n    SharedArrayBuffer: false\n};\nconst newGlobals2020 = {\n    BigInt: false,\n    BigInt64Array: false,\n    BigUint64Array: false,\n    globalThis: false\n};\n\nconst newGlobals2021 = {\n    AggregateError: false,\n    FinalizationRegistry: false,\n    WeakRef: false\n};\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\n/** @type {Map<string, import(\"../lib/shared/types\").Environment>} */\nexport default new Map(Object.entries({\n\n    // Language\n    builtin: {\n        globals: globals.es5\n    },\n    es6: {\n        globals: newGlobals2015,\n        parserOptions: {\n            ecmaVersion: 6\n        }\n    },\n    es2015: {\n        globals: newGlobals2015,\n        parserOptions: {\n            ecmaVersion: 6\n        }\n    },\n    es2016: {\n        globals: newGlobals2015,\n        parserOptions: {\n            ecmaVersion: 7\n        }\n    },\n    es2017: {\n        globals: { ...newGlobals2015, ...newGlobals2017 },\n        parserOptions: {\n            ecmaVersion: 8\n        }\n    },\n    es2018: {\n        globals: { ...newGlobals2015, ...newGlobals2017 },\n        parserOptions: {\n            ecmaVersion: 9\n        }\n    },\n    es2019: {\n        globals: { ...newGlobals2015, ...newGlobals2017 },\n        parserOptions: {\n            ecmaVersion: 10\n        }\n    },\n    es2020: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020 },\n        parserOptions: {\n            ecmaVersion: 11\n        }\n    },\n    es2021: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020, ...newGlobals2021 },\n        parserOptions: {\n            ecmaVersion: 12\n        }\n    },\n    es2022: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020, ...newGlobals2021 },\n        parserOptions: {\n            ecmaVersion: 13\n        }\n    },\n    es2023: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020, ...newGlobals2021 },\n        parserOptions: {\n            ecmaVersion: 14\n        }\n    },\n    es2024: {\n        globals: { ...newGlobals2015, ...newGlobals2017, ...newGlobals2020, ...newGlobals2021 },\n        parserOptions: {\n            ecmaVersion: 15\n        }\n    },\n\n    // Platforms\n    browser: {\n        globals: globals.browser\n    },\n    node: {\n        globals: globals.node,\n        parserOptions: {\n            ecmaFeatures: {\n                globalReturn: true\n            }\n        }\n    },\n    \"shared-node-browser\": {\n        globals: globals[\"shared-node-browser\"]\n    },\n    worker: {\n        globals: globals.worker\n    },\n    serviceworker: {\n        globals: globals.serviceworker\n    },\n\n    // Frameworks\n    commonjs: {\n        globals: globals.commonjs,\n        parserOptions: {\n            ecmaFeatures: {\n                globalReturn: true\n            }\n        }\n    },\n    amd: {\n        globals: globals.amd\n    },\n    mocha: {\n        globals: globals.mocha\n    },\n    jasmine: {\n        globals: globals.jasmine\n    },\n    jest: {\n        globals: globals.jest\n    },\n    phantomjs: {\n        globals: globals.phantomjs\n    },\n    jquery: {\n        globals: globals.jquery\n    },\n    qunit: {\n        globals: globals.qunit\n    },\n    prototypejs: {\n        globals: globals.prototypejs\n    },\n    shelljs: {\n        globals: globals.shelljs\n    },\n    meteor: {\n        globals: globals.meteor\n    },\n    mongo: {\n        globals: globals.mongo\n    },\n    protractor: {\n        globals: globals.protractor\n    },\n    applescript: {\n        globals: globals.applescript\n    },\n    nashorn: {\n        globals: globals.nashorn\n    },\n    atomtest: {\n        globals: globals.atomtest\n    },\n    embertest: {\n        globals: globals.embertest\n    },\n    webextensions: {\n        globals: globals.webextensions\n    },\n    greasemonkey: {\n        globals: globals.greasemonkey\n    }\n}));\n", "/**\n * @fileoverview Validates configs.\n * <AUTHOR>\n */\n\n/* eslint class-methods-use-this: \"off\" */\n\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport util from \"util\";\nimport * as ConfigOps from \"./config-ops.js\";\nimport { emitDeprecationWarning } from \"./deprecation-warnings.js\";\nimport ajvOrig from \"./ajv.js\";\nimport configSchema from \"../../conf/config-schema.js\";\nimport BuiltInEnvironments from \"../../conf/environments.js\";\n\nconst ajv = ajvOrig();\n\nconst ruleValidators = new WeakMap();\nconst noop = Function.prototype;\n\n//------------------------------------------------------------------------------\n// Private\n//------------------------------------------------------------------------------\nlet validateSchema;\nconst severityMap = {\n    error: 2,\n    warn: 1,\n    off: 0\n};\n\nconst validated = new WeakSet();\n\n//-----------------------------------------------------------------------------\n// Exports\n//-----------------------------------------------------------------------------\n\nexport default class ConfigValidator {\n    constructor({ builtInRules = new Map() } = {}) {\n        this.builtInRules = builtInRules;\n    }\n\n    /**\n     * Gets a complete options schema for a rule.\n     * @param {{create: Function, schema: (Array|null)}} rule A new-style rule object\n     * @returns {Object} JSON Schema for the rule's options.\n     */\n    getRuleOptionsSchema(rule) {\n        if (!rule) {\n            return null;\n        }\n\n        const schema = rule.schema || rule.meta && rule.meta.schema;\n\n        // Given a tuple of schemas, insert warning level at the beginning\n        if (Array.isArray(schema)) {\n            if (schema.length) {\n                return {\n                    type: \"array\",\n                    items: schema,\n                    minItems: 0,\n                    maxItems: schema.length\n                };\n            }\n            return {\n                type: \"array\",\n                minItems: 0,\n                maxItems: 0\n            };\n\n        }\n\n        // Given a full schema, leave it alone\n        return schema || null;\n    }\n\n    /**\n     * Validates a rule's severity and returns the severity value. Throws an error if the severity is invalid.\n     * @param {options} options The given options for the rule.\n     * @returns {number|string} The rule's severity value\n     */\n    validateRuleSeverity(options) {\n        const severity = Array.isArray(options) ? options[0] : options;\n        const normSeverity = typeof severity === \"string\" ? severityMap[severity.toLowerCase()] : severity;\n\n        if (normSeverity === 0 || normSeverity === 1 || normSeverity === 2) {\n            return normSeverity;\n        }\n\n        throw new Error(`\\tSeverity should be one of the following: 0 = off, 1 = warn, 2 = error (you passed '${util.inspect(severity).replace(/'/gu, \"\\\"\").replace(/\\n/gu, \"\")}').\\n`);\n\n    }\n\n    /**\n     * Validates the non-severity options passed to a rule, based on its schema.\n     * @param {{create: Function}} rule The rule to validate\n     * @param {Array} localOptions The options for the rule, excluding severity\n     * @returns {void}\n     */\n    validateRuleSchema(rule, localOptions) {\n        if (!ruleValidators.has(rule)) {\n            const schema = this.getRuleOptionsSchema(rule);\n\n            if (schema) {\n                ruleValidators.set(rule, ajv.compile(schema));\n            }\n        }\n\n        const validateRule = ruleValidators.get(rule);\n\n        if (validateRule) {\n            validateRule(localOptions);\n            if (validateRule.errors) {\n                throw new Error(validateRule.errors.map(\n                    error => `\\tValue ${JSON.stringify(error.data)} ${error.message}.\\n`\n                ).join(\"\"));\n            }\n        }\n    }\n\n    /**\n     * Validates a rule's options against its schema.\n     * @param {{create: Function}|null} rule The rule that the config is being validated for\n     * @param {string} ruleId The rule's unique name.\n     * @param {Array|number} options The given options for the rule.\n     * @param {string|null} source The name of the configuration source to report in any errors. If null or undefined,\n     * no source is prepended to the message.\n     * @returns {void}\n     */\n    validateRuleOptions(rule, ruleId, options, source = null) {\n        try {\n            const severity = this.validateRuleSeverity(options);\n\n            if (severity !== 0) {\n                this.validateRuleSchema(rule, Array.isArray(options) ? options.slice(1) : []);\n            }\n        } catch (err) {\n            const enhancedMessage = `Configuration for rule \"${ruleId}\" is invalid:\\n${err.message}`;\n\n            if (typeof source === \"string\") {\n                throw new Error(`${source}:\\n\\t${enhancedMessage}`);\n            } else {\n                throw new Error(enhancedMessage);\n            }\n        }\n    }\n\n    /**\n     * Validates an environment object\n     * @param {Object} environment The environment config object to validate.\n     * @param {string} source The name of the configuration source to report in any errors.\n     * @param {function(envId:string): Object} [getAdditionalEnv] A map from strings to loaded environments.\n     * @returns {void}\n     */\n    validateEnvironment(\n        environment,\n        source,\n        getAdditionalEnv = noop\n    ) {\n\n        // not having an environment is ok\n        if (!environment) {\n            return;\n        }\n\n        Object.keys(environment).forEach(id => {\n            const env = getAdditionalEnv(id) || BuiltInEnvironments.get(id) || null;\n\n            if (!env) {\n                const message = `${source}:\\n\\tEnvironment key \"${id}\" is unknown\\n`;\n\n                throw new Error(message);\n            }\n        });\n    }\n\n    /**\n     * Validates a rules config object\n     * @param {Object} rulesConfig The rules config object to validate.\n     * @param {string} source The name of the configuration source to report in any errors.\n     * @param {function(ruleId:string): Object} getAdditionalRule A map from strings to loaded rules\n     * @returns {void}\n     */\n    validateRules(\n        rulesConfig,\n        source,\n        getAdditionalRule = noop\n    ) {\n        if (!rulesConfig) {\n            return;\n        }\n\n        Object.keys(rulesConfig).forEach(id => {\n            const rule = getAdditionalRule(id) || this.builtInRules.get(id) || null;\n\n            this.validateRuleOptions(rule, id, rulesConfig[id], source);\n        });\n    }\n\n    /**\n     * Validates a `globals` section of a config file\n     * @param {Object} globalsConfig The `globals` section\n     * @param {string|null} source The name of the configuration source to report in the event of an error.\n     * @returns {void}\n     */\n    validateGlobals(globalsConfig, source = null) {\n        if (!globalsConfig) {\n            return;\n        }\n\n        Object.entries(globalsConfig)\n            .forEach(([configuredGlobal, configuredValue]) => {\n                try {\n                    ConfigOps.normalizeConfigGlobal(configuredValue);\n                } catch (err) {\n                    throw new Error(`ESLint configuration of global '${configuredGlobal}' in ${source} is invalid:\\n${err.message}`);\n                }\n            });\n    }\n\n    /**\n     * Validate `processor` configuration.\n     * @param {string|undefined} processorName The processor name.\n     * @param {string} source The name of config file.\n     * @param {function(id:string): Processor} getProcessor The getter of defined processors.\n     * @returns {void}\n     */\n    validateProcessor(processorName, source, getProcessor) {\n        if (processorName && !getProcessor(processorName)) {\n            throw new Error(`ESLint configuration of processor in '${source}' is invalid: '${processorName}' was not found.`);\n        }\n    }\n\n    /**\n     * Formats an array of schema validation errors.\n     * @param {Array} errors An array of error messages to format.\n     * @returns {string} Formatted error message\n     */\n    formatErrors(errors) {\n        return errors.map(error => {\n            if (error.keyword === \"additionalProperties\") {\n                const formattedPropertyPath = error.dataPath.length ? `${error.dataPath.slice(1)}.${error.params.additionalProperty}` : error.params.additionalProperty;\n\n                return `Unexpected top-level property \"${formattedPropertyPath}\"`;\n            }\n            if (error.keyword === \"type\") {\n                const formattedField = error.dataPath.slice(1);\n                const formattedExpectedType = Array.isArray(error.schema) ? error.schema.join(\"/\") : error.schema;\n                const formattedValue = JSON.stringify(error.data);\n\n                return `Property \"${formattedField}\" is the wrong type (expected ${formattedExpectedType} but got \\`${formattedValue}\\`)`;\n            }\n\n            const field = error.dataPath[0] === \".\" ? error.dataPath.slice(1) : error.dataPath;\n\n            return `\"${field}\" ${error.message}. Value: ${JSON.stringify(error.data)}`;\n        }).map(message => `\\t- ${message}.\\n`).join(\"\");\n    }\n\n    /**\n     * Validates the top level properties of the config object.\n     * @param {Object} config The config object to validate.\n     * @param {string} source The name of the configuration source to report in any errors.\n     * @returns {void}\n     */\n    validateConfigSchema(config, source = null) {\n        validateSchema = validateSchema || ajv.compile(configSchema);\n\n        if (!validateSchema(config)) {\n            throw new Error(`ESLint configuration in ${source} is invalid:\\n${this.formatErrors(validateSchema.errors)}`);\n        }\n\n        if (Object.hasOwnProperty.call(config, \"ecmaFeatures\")) {\n            emitDeprecationWarning(source, \"ESLINT_LEGACY_ECMAFEATURES\");\n        }\n    }\n\n    /**\n     * Validates an entire config object.\n     * @param {Object} config The config object to validate.\n     * @param {string} source The name of the configuration source to report in any errors.\n     * @param {function(ruleId:string): Object} [getAdditionalRule] A map from strings to loaded rules.\n     * @param {function(envId:string): Object} [getAdditionalEnv] A map from strings to loaded envs.\n     * @returns {void}\n     */\n    validate(config, source, getAdditionalRule, getAdditionalEnv) {\n        this.validateConfigSchema(config, source);\n        this.validateRules(config.rules, source, getAdditionalRule);\n        this.validateEnvironment(config.env, source, getAdditionalEnv);\n        this.validateGlobals(config.globals, source);\n\n        for (const override of config.overrides || []) {\n            this.validateRules(override.rules, source, getAdditionalRule);\n            this.validateEnvironment(override.env, source, getAdditionalEnv);\n            this.validateGlobals(config.globals, source);\n        }\n    }\n\n    /**\n     * Validate config array object.\n     * @param {ConfigArray} configArray The config array to validate.\n     * @returns {void}\n     */\n    validateConfigArray(configArray) {\n        const getPluginEnv = Map.prototype.get.bind(configArray.pluginEnvironments);\n        const getPluginProcessor = Map.prototype.get.bind(configArray.pluginProcessors);\n        const getPluginRule = Map.prototype.get.bind(configArray.pluginRules);\n\n        // Validate.\n        for (const element of configArray) {\n            if (validated.has(element)) {\n                continue;\n            }\n            validated.add(element);\n\n            this.validateEnvironment(element.env, element.name, getPluginEnv);\n            this.validateGlobals(element.globals, element.name);\n            this.validateProcessor(element.processor, element.name, getPluginProcessor);\n            this.validateRules(element.rules, element.name, getPluginRule);\n        }\n    }\n\n}\n", "/**\n * @fileoverview Common helpers for naming of plugins, formatters and configs\n */\n\nconst NAMESPACE_REGEX = /^@.*\\//iu;\n\n/**\n * Brings package name to correct format based on prefix\n * @param {string} name The name of the package.\n * @param {string} prefix Can be either \"eslint-plugin\", \"eslint-config\" or \"eslint-formatter\"\n * @returns {string} Normalized name of the package\n * @private\n */\nfunction normalizePackageName(name, prefix) {\n    let normalizedName = name;\n\n    /**\n     * On Windows, name can come in with Windows slashes instead of Unix slashes.\n     * Normalize to Unix first to avoid errors later on.\n     * https://github.com/eslint/eslint/issues/5644\n     */\n    if (normalizedName.includes(\"\\\\\")) {\n        normalizedName = normalizedName.replace(/\\\\/gu, \"/\");\n    }\n\n    if (normalizedName.charAt(0) === \"@\") {\n\n        /**\n         * it's a scoped package\n         * package name is the prefix, or just a username\n         */\n        const scopedPackageShortcutRegex = new RegExp(`^(@[^/]+)(?:/(?:${prefix})?)?$`, \"u\"),\n            scopedPackageNameRegex = new RegExp(`^${prefix}(-|$)`, \"u\");\n\n        if (scopedPackageShortcutRegex.test(normalizedName)) {\n            normalizedName = normalizedName.replace(scopedPackageShortcutRegex, `$1/${prefix}`);\n        } else if (!scopedPackageNameRegex.test(normalizedName.split(\"/\")[1])) {\n\n            /**\n             * for scoped packages, insert the prefix after the first / unless\n             * the path is already @scope/eslint or @scope/eslint-xxx-yyy\n             */\n            normalizedName = normalizedName.replace(/^@([^/]+)\\/(.*)$/u, `@$1/${prefix}-$2`);\n        }\n    } else if (!normalizedName.startsWith(`${prefix}-`)) {\n        normalizedName = `${prefix}-${normalizedName}`;\n    }\n\n    return normalizedName;\n}\n\n/**\n * Removes the prefix from a fullname.\n * @param {string} fullname The term which may have the prefix.\n * @param {string} prefix The prefix to remove.\n * @returns {string} The term without prefix.\n */\nfunction getShorthandName(fullname, prefix) {\n    if (fullname[0] === \"@\") {\n        let matchResult = new RegExp(`^(@[^/]+)/${prefix}$`, \"u\").exec(fullname);\n\n        if (matchResult) {\n            return matchResult[1];\n        }\n\n        matchResult = new RegExp(`^(@[^/]+)/${prefix}-(.+)$`, \"u\").exec(fullname);\n        if (matchResult) {\n            return `${matchResult[1]}/${matchResult[2]}`;\n        }\n    } else if (fullname.startsWith(`${prefix}-`)) {\n        return fullname.slice(prefix.length + 1);\n    }\n\n    return fullname;\n}\n\n/**\n * Gets the scope (namespace) of a term.\n * @param {string} term The term which may have the namespace.\n * @returns {string} The namespace of the term if it has one.\n */\nfunction getNamespaceFromTerm(term) {\n    const match = term.match(NAMESPACE_REGEX);\n\n    return match ? match[0] : \"\";\n}\n\n//------------------------------------------------------------------------------\n// Public Interface\n//------------------------------------------------------------------------------\n\nexport {\n    normalizePackageName,\n    getShorthandName,\n    getNamespaceFromTerm\n};\n", "/**\n * @fileoverview Package exports for @eslint/eslintrc\n * <AUTHOR>\n */\n//------------------------------------------------------------------------------\n// Requirements\n//------------------------------------------------------------------------------\n\nimport * as ConfigOps from \"./shared/config-ops.js\";\nimport ConfigValidator from \"./shared/config-validator.js\";\nimport * as naming from \"./shared/naming.js\";\nimport environments from \"../conf/environments.js\";\n\n//-----------------------------------------------------------------------------\n// Exports\n//-----------------------------------------------------------------------------\n\nconst Legacy = {\n    environments,\n\n    // shared\n    ConfigOps,\n    ConfigValidator,\n    naming\n};\n\nexport {\n    Legacy\n};\n"], "names": ["path", "Ajv", "globals", "util", "BuiltInEnvironments", "ConfigOps.normalizeConfigGlobal"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qBAAqB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;AACtD,IAAI,aAAa,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,KAAK;AACxE,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC3B,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,EAAE,EAAE,CAAC;AACV,IAAI,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,UAAU,EAAE;AACrC,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AACjF;AACA,IAAI,IAAI,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,EAAE;AAC3E,QAAQ,OAAO,aAAa,CAAC;AAC7B,KAAK;AACL;AACA,IAAI,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;AAC3C,QAAQ,OAAO,aAAa,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC;AAC/D,KAAK;AACL;AACA,IAAI,OAAO,CAAC,CAAC;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,MAAM,EAAE;AACpC;AACA,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;AACtB,QAAQ,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI;AACpD,YAAY,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACpD;AACA,YAAY,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AAChD,gBAAgB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,qBAAqB,CAAC,UAAU,CAAC,IAAI,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACrG,aAAa,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,UAAU,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AACvF,gBAAgB,UAAU,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACjG,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,UAAU,EAAE;AACrC,IAAI,OAAO,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC7C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,UAAU,EAAE;AACrC,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AAC1E;AACA,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AACtC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;AAC1C,KAAK;AACL,IAAI,OAAO,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACrD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,MAAM,EAAE;AACtC,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC,eAAe,EAAE;AAChD,IAAI,QAAQ,eAAe;AAC3B,QAAQ,KAAK,KAAK;AAClB,YAAY,OAAO,KAAK,CAAC;AACzB;AACA,QAAQ,KAAK,IAAI,CAAC;AAClB,QAAQ,KAAK,MAAM,CAAC;AACpB,QAAQ,KAAK,WAAW,CAAC;AACzB,QAAQ,KAAK,UAAU;AACvB,YAAY,OAAO,UAAU,CAAC;AAC9B;AACA,QAAQ,KAAK,IAAI,CAAC;AAClB,QAAQ,KAAK,KAAK,CAAC;AACnB,QAAQ,KAAK,OAAO,CAAC;AACrB,QAAQ,KAAK,UAAU,CAAC;AACxB,QAAQ,KAAK,UAAU;AACvB,YAAY,OAAO,UAAU,CAAC;AAC9B;AACA,QAAQ;AACR,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,kFAAkF,CAAC,CAAC,CAAC;AACrI,KAAK;AACL;;;;;;;;;;;;AC7HA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,0BAA0B,GAAG;AACnC,IAAI,0BAA0B;AAC9B,QAAQ,0EAA0E;AAClF,IAAI,2BAA2B;AAC/B,QAAQ,qDAAqD;AAC7D,QAAQ,gEAAgE;AACxE,IAAI,+BAA+B;AACnC,QAAQ,qDAAqD;AAC7D,QAAQ,kEAAkE;AAC1E,QAAQ,kEAAkE;AAC1E,CAAC,CAAC;AACF;AACA,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,sBAAsB,CAAC,MAAM,EAAE,SAAS,EAAE;AACnD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC3D;AACA,IAAI,IAAI,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC5C,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACvC;AACA,IAAI,MAAM,GAAG,GAAGA,wBAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;AACrD,IAAI,MAAM,OAAO,GAAG,0BAA0B,CAAC,SAAS,CAAC,CAAC;AAC1D;AACA,IAAI,OAAO,CAAC,WAAW;AACvB,QAAQ,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,CAAC;AACxC,QAAQ,oBAAoB;AAC5B,QAAQ,SAAS;AACjB,KAAK,CAAC;AACN;;ACtDA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,GAAG;AACnB,IAAI,EAAE,EAAE,yCAAyC;AACjD,IAAI,OAAO,EAAE,yCAAyC;AACtD,IAAI,WAAW,EAAE,yBAAyB;AAC1C,IAAI,WAAW,EAAE;AACjB,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,QAAQ,EAAE,CAAC;AACvB,YAAY,KAAK,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAChC,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,IAAI,EAAE,SAAS;AAC3B,YAAY,OAAO,EAAE,CAAC;AACtB,SAAS;AACT,QAAQ,uBAAuB,EAAE;AACjC,YAAY,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,+BAA+B,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9E,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;AACvF,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AACrC,YAAY,QAAQ,EAAE,CAAC;AACvB,YAAY,WAAW,EAAE,IAAI;AAC7B,SAAS;AACT,KAAK;AACL,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,UAAU,EAAE;AAChB,QAAQ,EAAE,EAAE;AACZ,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,KAAK,EAAE;AACf,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,OAAO,EAAE,GAAG;AACpB,QAAQ,UAAU,EAAE;AACpB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,gBAAgB,EAAE,IAAI;AAClC,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,IAAI,EAAE,SAAS;AAC3B,YAAY,OAAO,EAAE,KAAK;AAC1B,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,YAAY,IAAI,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,IAAI,EAAE,SAAS;AAC3B,YAAY,OAAO,EAAE,KAAK;AAC1B,SAAS;AACT,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AAC5D,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;AACpE,QAAQ,OAAO,EAAE;AACjB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,MAAM,EAAE,OAAO;AAC3B,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;AACnC,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAE;AAC7B,aAAa;AACb,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,KAAK,EAAE;AACf,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAE;AAC7B,gBAAgB,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACrD,aAAa;AACb,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AAC3D,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;AACnE,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,SAAS;AAC3B,YAAY,OAAO,EAAE,KAAK;AAC1B,SAAS;AACT,QAAQ,aAAa,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AAChE,QAAQ,aAAa,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;AACxE,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACvD,QAAQ,oBAAoB,EAAE;AAC9B,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;AACnC,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAE;AAC7B,aAAa;AACb,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,oBAAoB,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC/C,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,oBAAoB,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC/C,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,iBAAiB,EAAE;AAC3B,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,oBAAoB,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC/C,YAAY,OAAO,EAAE,GAAG;AACxB,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,oBAAoB,EAAE;AAClC,gBAAgB,KAAK,EAAE;AACvB,oBAAoB,EAAE,IAAI,EAAE,GAAG,EAAE;AACjC,oBAAoB,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACzD,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,QAAQ,EAAE,CAAC;AACvB,YAAY,WAAW,EAAE,IAAI;AAC7B,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACrD,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,OAAO;AACjC,oBAAoB,KAAK,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AAChE,oBAAoB,QAAQ,EAAE,CAAC;AAC/B,oBAAoB,WAAW,EAAE,IAAI;AACrC,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAClC,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACpD,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACpD,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;AACpD,QAAQ,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC1B,KAAK;AACL,IAAI,YAAY,EAAE;AAClB,QAAQ,gBAAgB,EAAE,CAAC,SAAS,CAAC;AACrC,QAAQ,gBAAgB,EAAE,CAAC,SAAS,CAAC;AACrC,KAAK;AACL,IAAI,OAAO,EAAE,GAAG;AAChB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,cAAe,CAAC,iBAAiB,GAAG,EAAE,KAAK;AAC3C,IAAI,MAAM,GAAG,GAAG,IAAIC,uBAAG,CAAC;AACxB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,WAAW,EAAE,QAAQ;AAC7B,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,QAAQ,EAAE,MAAM;AACxB,QAAQ,GAAG,iBAAiB;AAC5B,KAAK,CAAC,CAAC;AACP;AACA,IAAI,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AAClC;AACA,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC,EAAE,CAAC;AAC1C;AACA,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;;AC9LD;AACA;AACA;AACA;AACA;AACA,MAAM,oBAAoB,GAAG;AAC7B,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC/B,IAAI,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC3B,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AACtD,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC/B,IAAI,SAAS,EAAE;AACf,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,8BAA8B,EAAE;AACvD,QAAQ,eAAe,EAAE,KAAK;AAC9B,KAAK;AACL,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;AACxC,IAAI,aAAa,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AACrC,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;AAC9B,IAAI,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AACjC,IAAI,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC7B,IAAI,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAChC,IAAI,cAAc,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;AACvC,IAAI,6BAA6B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;AACtD;AACA,IAAI,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AACpC,CAAC,CAAC;AACF;AACA,MAAM,YAAY,GAAG;AACrB,IAAI,WAAW,EAAE;AACjB,QAAQ,eAAe,EAAE;AACzB,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAE;AAClC,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,OAAO;AACjC,oBAAoB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC7C,oBAAoB,eAAe,EAAE,KAAK;AAC1C,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,uBAAuB,EAAE;AACjC,YAAY,KAAK,EAAE;AACnB,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAE;AAClC,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,OAAO;AACjC,oBAAoB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC7C,oBAAoB,eAAe,EAAE,KAAK;AAC1C,oBAAoB,QAAQ,EAAE,CAAC;AAC/B,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA,QAAQ,YAAY,EAAE;AACtB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,UAAU,EAAE;AACxB,gBAAgB,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;AACzC,gBAAgB,cAAc,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AACzE,gBAAgB,GAAG,oBAAoB;AACvC,aAAa;AACb,YAAY,oBAAoB,EAAE,KAAK;AACvC,SAAS;AACT;AACA;AACA,QAAQ,cAAc,EAAE;AACxB,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,UAAU,EAAE;AACxB,gBAAgB,aAAa,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;AACxE,gBAAgB,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;AACxE,gBAAgB,GAAG,oBAAoB;AACvC,aAAa;AACb,YAAY,QAAQ,EAAE,CAAC,OAAO,CAAC;AAC/B,YAAY,oBAAoB,EAAE,KAAK;AACvC,SAAS;AACT,KAAK;AACL;AACA,IAAI,IAAI,EAAE,4BAA4B;AACtC,CAAC;;AC5ED;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE;AAChC,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;AACpB;AACA,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACxD,QAAQ,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;AACpD,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC9B,SAAS;AACT,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD;AACA,MAAM,cAAc,GAAG,OAAO,CAACC,2BAAO,CAAC,MAAM,EAAEA,2BAAO,CAAC,GAAG,CAAC,CAAC;AAC5D,MAAM,cAAc,GAAG;AACvB,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,iBAAiB,EAAE,KAAK;AAC5B,CAAC,CAAC;AACF,MAAM,cAAc,GAAG;AACvB,IAAI,MAAM,EAAE,KAAK;AACjB,IAAI,aAAa,EAAE,KAAK;AACxB,IAAI,cAAc,EAAE,KAAK;AACzB,IAAI,UAAU,EAAE,KAAK;AACrB,CAAC,CAAC;AACF;AACA,MAAM,cAAc,GAAG;AACvB,IAAI,cAAc,EAAE,KAAK;AACzB,IAAI,oBAAoB,EAAE,KAAK;AAC/B,IAAI,OAAO,EAAE,KAAK;AAClB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,mBAAe,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC;AACtC;AACA;AACA,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,GAAG;AAC5B,KAAK;AACL,IAAI,GAAG,EAAE;AACT,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AACzD,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AACzD,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AACzD,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC5E,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC/F,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC/F,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC/F,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAE,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,EAAE;AAC/F,QAAQ,aAAa,EAAE;AACvB,YAAY,WAAW,EAAE,EAAE;AAC3B,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,OAAO;AAChC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,QAAQ,OAAO,EAAEA,2BAAO,CAAC,IAAI;AAC7B,QAAQ,aAAa,EAAE;AACvB,YAAY,YAAY,EAAE;AAC1B,gBAAgB,YAAY,EAAE,IAAI;AAClC,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAI,qBAAqB,EAAE;AAC3B,QAAQ,OAAO,EAAEA,2BAAO,CAAC,qBAAqB,CAAC;AAC/C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAEA,2BAAO,CAAC,MAAM;AAC/B,KAAK;AACL,IAAI,aAAa,EAAE;AACnB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,aAAa;AACtC,KAAK;AACL;AACA;AACA,IAAI,QAAQ,EAAE;AACd,QAAQ,OAAO,EAAEA,2BAAO,CAAC,QAAQ;AACjC,QAAQ,aAAa,EAAE;AACvB,YAAY,YAAY,EAAE;AAC1B,gBAAgB,YAAY,EAAE,IAAI;AAClC,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAI,GAAG,EAAE;AACT,QAAQ,OAAO,EAAEA,2BAAO,CAAC,GAAG;AAC5B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,OAAO,EAAEA,2BAAO,CAAC,KAAK;AAC9B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,OAAO;AAChC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,QAAQ,OAAO,EAAEA,2BAAO,CAAC,IAAI;AAC7B,KAAK;AACL,IAAI,SAAS,EAAE;AACf,QAAQ,OAAO,EAAEA,2BAAO,CAAC,SAAS;AAClC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAEA,2BAAO,CAAC,MAAM;AAC/B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,OAAO,EAAEA,2BAAO,CAAC,KAAK;AAC9B,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,WAAW;AACpC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,OAAO;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,OAAO,EAAEA,2BAAO,CAAC,MAAM;AAC/B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,OAAO,EAAEA,2BAAO,CAAC,KAAK;AAC9B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,UAAU;AACnC,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,WAAW;AACpC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,QAAQ,OAAO,EAAEA,2BAAO,CAAC,OAAO;AAChC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,QAAQ,OAAO,EAAEA,2BAAO,CAAC,QAAQ;AACjC,KAAK;AACL,IAAI,SAAS,EAAE;AACf,QAAQ,OAAO,EAAEA,2BAAO,CAAC,SAAS;AAClC,KAAK;AACL,IAAI,aAAa,EAAE;AACnB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,aAAa;AACtC,KAAK;AACL,IAAI,YAAY,EAAE;AAClB,QAAQ,OAAO,EAAEA,2BAAO,CAAC,YAAY;AACrC,KAAK;AACL,CAAC,CAAC,CAAC;;ACtNH;AACA;AACA;AACA;AAcA;AACA,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC;AACtB;AACA,MAAM,cAAc,GAAG,IAAI,OAAO,EAAE,CAAC;AACrC,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC;AAChC;AACA;AACA;AACA;AACA,IAAI,cAAc,CAAC;AACnB,MAAM,WAAW,GAAG;AACpB,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,GAAG,EAAE,CAAC;AACV,CAAC,CAAC;AACF;AACA,MAAM,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;AAChC;AACA;AACA;AACA;AACA;AACe,MAAM,eAAe,CAAC;AACrC,IAAI,WAAW,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AACnD,QAAQ,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACzC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,CAAC,IAAI,EAAE;AAC/B,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT;AACA,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACpE;AACA;AACA,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACnC,YAAY,IAAI,MAAM,CAAC,MAAM,EAAE;AAC/B,gBAAgB,OAAO;AACvB,oBAAoB,IAAI,EAAE,OAAO;AACjC,oBAAoB,KAAK,EAAE,MAAM;AACjC,oBAAoB,QAAQ,EAAE,CAAC;AAC/B,oBAAoB,QAAQ,EAAE,MAAM,CAAC,MAAM;AAC3C,iBAAiB,CAAC;AAClB,aAAa;AACb,YAAY,OAAO;AACnB,gBAAgB,IAAI,EAAE,OAAO;AAC7B,gBAAgB,QAAQ,EAAE,CAAC;AAC3B,gBAAgB,QAAQ,EAAE,CAAC;AAC3B,aAAa,CAAC;AACd;AACA,SAAS;AACT;AACA;AACA,QAAQ,OAAO,MAAM,IAAI,IAAI,CAAC;AAC9B,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,CAAC,OAAO,EAAE;AAClC,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AACvE,QAAQ,MAAM,YAAY,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,GAAG,QAAQ,CAAC;AAC3G;AACA,QAAQ,IAAI,YAAY,KAAK,CAAC,IAAI,YAAY,KAAK,CAAC,IAAI,YAAY,KAAK,CAAC,EAAE;AAC5E,YAAY,OAAO,YAAY,CAAC;AAChC,SAAS;AACT;AACA,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,qFAAqF,EAAEC,wBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACxL;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC3C,QAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACvC,YAAY,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAC3D;AACA,YAAY,IAAI,MAAM,EAAE;AACxB,gBAAgB,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9D,aAAa;AACb,SAAS;AACT;AACA,QAAQ,MAAM,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACtD;AACA,QAAQ,IAAI,YAAY,EAAE;AAC1B,YAAY,YAAY,CAAC,YAAY,CAAC,CAAC;AACvC,YAAY,IAAI,YAAY,CAAC,MAAM,EAAE;AACrC,gBAAgB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG;AACvD,oBAAoB,KAAK,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;AACxF,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,EAAE;AAC9D,QAAQ,IAAI;AACZ,YAAY,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAChE;AACA,YAAY,IAAI,QAAQ,KAAK,CAAC,EAAE;AAChC,gBAAgB,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9F,aAAa;AACb,SAAS,CAAC,OAAO,GAAG,EAAE;AACtB,YAAY,MAAM,eAAe,GAAG,CAAC,wBAAwB,EAAE,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AACrG;AACA,YAAY,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC5C,gBAAgB,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;AACpE,aAAa,MAAM;AACnB,gBAAgB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;AACjD,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB;AACvB,QAAQ,WAAW;AACnB,QAAQ,MAAM;AACd,QAAQ,gBAAgB,GAAG,IAAI;AAC/B,MAAM;AACN;AACA;AACA,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,OAAO;AACnB,SAAS;AACT;AACA,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI;AAC/C,YAAY,MAAM,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAIC,YAAmB,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;AACpF;AACA,YAAY,IAAI,CAAC,GAAG,EAAE;AACtB,gBAAgB,MAAM,OAAO,GAAG,CAAC,EAAE,MAAM,CAAC,sBAAsB,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC;AACrF;AACA,gBAAgB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACzC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa;AACjB,QAAQ,WAAW;AACnB,QAAQ,MAAM;AACd,QAAQ,iBAAiB,GAAG,IAAI;AAChC,MAAM;AACN,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAY,OAAO;AACnB,SAAS;AACT;AACA,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI;AAC/C,YAAY,MAAM,IAAI,GAAG,iBAAiB,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;AACpF;AACA,YAAY,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACxE,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,eAAe,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,EAAE;AAClD,QAAQ,IAAI,CAAC,aAAa,EAAE;AAC5B,YAAY,OAAO;AACnB,SAAS;AACT;AACA,QAAQ,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;AACrC,aAAa,OAAO,CAAC,CAAC,CAAC,gBAAgB,EAAE,eAAe,CAAC,KAAK;AAC9D,gBAAgB,IAAI;AACpB,oBAAoBC,qBAA+B,CAAC,eAAe,CAAC,CAAC;AACrE,iBAAiB,CAAC,OAAO,GAAG,EAAE;AAC9B,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,gCAAgC,EAAE,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,cAAc,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrI,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE;AAC3D,QAAQ,IAAI,aAAa,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;AAC3D,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,sCAAsC,EAAE,MAAM,CAAC,eAAe,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC9H,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,CAAC,MAAM,EAAE;AACzB,QAAQ,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI;AACnC,YAAY,IAAI,KAAK,CAAC,OAAO,KAAK,sBAAsB,EAAE;AAC1D,gBAAgB,MAAM,qBAAqB,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC;AACxK;AACA,gBAAgB,OAAO,CAAC,+BAA+B,EAAE,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAClF,aAAa;AACb,YAAY,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;AAC1C,gBAAgB,MAAM,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/D,gBAAgB,MAAM,qBAAqB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AAClH,gBAAgB,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAClE;AACA,gBAAgB,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,8BAA8B,EAAE,qBAAqB,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;AAC1I,aAAa;AACb;AACA,YAAY,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;AAC/F;AACA,YAAY,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvF,SAAS,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE;AAChD,QAAQ,cAAc,GAAG,cAAc,IAAI,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AACrE;AACA,QAAQ,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AACrC,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,wBAAwB,EAAE,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1H,SAAS;AACT;AACA,QAAQ,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE;AAChE,YAAY,sBAAsB,CAAC,MAAM,EAAE,4BAA4B,CAAC,CAAC;AACzE,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,gBAAgB,EAAE;AAClE,QAAQ,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAClD,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;AACpE,QAAQ,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;AACvE,QAAQ,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACrD;AACA,QAAQ,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,IAAI,EAAE,EAAE;AACvD,YAAY,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC1E,YAAY,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;AAC7E,YAAY,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACzD,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB,CAAC,WAAW,EAAE;AACrC,QAAQ,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AACpF,QAAQ,MAAM,kBAAkB,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;AACxF,QAAQ,MAAM,aAAa,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAC9E;AACA;AACA,QAAQ,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;AAC3C,YAAY,IAAI,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACxC,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACnC;AACA,YAAY,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AAC9E,YAAY,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;AAChE,YAAY,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;AACxF,YAAY,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC3E,SAAS;AACT,KAAK;AACL;AACA;;ACpUA;AACA;AACA;AACA;AACA,MAAM,eAAe,GAAG,UAAU,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE;AAC5C,IAAI,IAAI,cAAc,GAAG,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACvC,QAAQ,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC7D,KAAK;AACL;AACA,IAAI,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC1C;AACA;AACA;AACA;AACA;AACA,QAAQ,MAAM,0BAA0B,GAAG,IAAI,MAAM,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;AAC5F,YAAY,sBAAsB,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AACxE;AACA,QAAQ,IAAI,0BAA0B,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;AAC7D,YAAY,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,0BAA0B,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AAChG,SAAS,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC/E;AACA;AACA;AACA;AACA;AACA,YAAY,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7F,SAAS;AACT,KAAK,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AACzD,QAAQ,cAAc,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;AACvD,KAAK;AACL;AACA,IAAI,OAAO,cAAc,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE;AAC5C,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC7B,QAAQ,IAAI,WAAW,GAAG,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjF;AACA,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAY,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;AAClC,SAAS;AACT;AACA,QAAQ,WAAW,GAAG,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClF,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAY,OAAO,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,SAAS;AACT,KAAK,MAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAClD,QAAQ,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACjD,KAAK;AACL;AACA,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,IAAI,EAAE;AACpC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AAC9C;AACA,IAAI,OAAO,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACjC;;;;;;;;;ACrFA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AACA;AACK,MAAC,MAAM,GAAG;AACf,IAAI,YAAY;AAChB;AACA;AACA,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,MAAM;AACV;;;;"}