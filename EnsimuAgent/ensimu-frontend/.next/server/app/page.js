/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fcomponents%2Fproviders.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fcomponents%2Fproviders.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGYyUyRlVzZXJzJTJGYXRvbWdldGludGhlcSUyRkRvd25sb2FkcyUyRkVuc2ltdUFnZW50JTJGRW5zaW11QWdlbnQlMkZlbnNpbXUtZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZtbnQlMkZjJTJGVXNlcnMlMkZhdG9tZ2V0aW50aGVxJTJGRG93bmxvYWRzJTJGRW5zaW11QWdlbnQlMkZFbnNpbXVBZ2VudCUyRmVuc2ltdS1mcm9udGVuZCUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGbW50JTJGYyUyRlVzZXJzJTJGYXRvbWdldGludGhlcSUyRkRvd25sb2FkcyUyRkVuc2ltdUFnZW50JTJGRW5zaW11QWdlbnQlMkZlbnNpbXUtZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbnNpbXUtZnJvbnRlbmQvPzYyNWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbW50L2MvVXNlcnMvYXRvbWdldGludGhlcS9Eb3dubG9hZHMvRW5zaW11QWdlbnQvRW5zaW11QWdlbnQvZW5zaW11LWZyb250ZW5kL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fcomponents%2Fproviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGYyUyRlVzZXJzJTJGYXRvbWdldGludGhlcSUyRkRvd25sb2FkcyUyRkVuc2ltdUFnZW50JTJGRW5zaW11QWdlbnQlMkZlbnNpbXUtZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbnNpbXUtZnJvbnRlbmQvPzRlOWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbW50L2MvVXNlcnMvYXRvbWdldGludGhlcS9Eb3dubG9hZHMvRW5zaW11QWdlbnQvRW5zaW11QWdlbnQvZW5zaW11LWZyb250ZW5kL3NyYy9hcHAvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_sections_HeroSection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/sections/HeroSection */ \"(ssr)/./src/components/sections/HeroSection.tsx\");\n/* harmony import */ var _components_sections_FeatureCards__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/FeatureCards */ \"(ssr)/./src/components/sections/FeatureCards.tsx\");\n/* harmony import */ var _components_sections_WorkflowSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/WorkflowSection */ \"(ssr)/./src/components/sections/WorkflowSection.tsx\");\n/* harmony import */ var _components_sections_StatsSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/StatsSection */ \"(ssr)/./src/components/sections/StatsSection.tsx\");\n/* harmony import */ var _components_sections_QuickStartGuide__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/QuickStartGuide */ \"(ssr)/./src/components/sections/QuickStartGuide.tsx\");\n/* harmony import */ var _components_layout_Navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/Navigation */ \"(ssr)/./src/components/layout/Navigation.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navigation__WEBPACK_IMPORTED_MODULE_6__.Navigation, {}, void 0, false, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-12 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_HeroSection__WEBPACK_IMPORTED_MODULE_1__.HeroSection, {}, void 0, false, {\n                                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_FeatureCards__WEBPACK_IMPORTED_MODULE_2__.FeatureCards, {}, void 0, false, {\n                                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_StatsSection__WEBPACK_IMPORTED_MODULE_4__.StatsSection, {}, void 0, false, {\n                                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_QuickStartGuide__WEBPACK_IMPORTED_MODULE_5__.QuickStartGuide, {}, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_WorkflowSection__WEBPACK_IMPORTED_MODULE_3__.WorkflowSection, {}, void 0, false, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_7__.Footer, {}, void 0, false, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.footer, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.6\n        },\n        className: \"bg-slate-900/95 border-t border-slate-700/50 mt-16 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-2\",\n                                    children: \"Ensimu.space\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-400 mb-2\",\n                                    children: \"AI-Powered Engineering Simulation Preprocessing\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-500 text-sm\",\n                                    children: \"Automating the complex art of CAE preprocessing through multi-agent artificial intelligence.\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-500 text-sm\",\n                                    children: \"Powered by Advanced AI Agents\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-slate-400 hover:text-white transition-colors\",\n                                            children: \"\\uD83D\\uDCDA Documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-slate-400 hover:text-white transition-colors\",\n                                            children: \"\\uD83D\\uDCAC Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-slate-400 hover:text-white transition-colors\",\n                                            children: \"\\uD83D\\uDC19 GitHub\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-slate-700/50 mt-8 pt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-500 text-sm\",\n                        children: \"\\xa9 2024 Ensimu.space. All rights reserved. | Built with Next.js & AI ❤️\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Footer.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\nfunction Navigation() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-slate-900/95 backdrop-blur-sm border-b border-slate-700/50 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-ensimu-400 to-ensimu-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"E\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"Ensimu.space\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"text-slate-300 hover:text-white transition-colors\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/upload\",\n                                    className: \"text-slate-300 hover:text-white transition-colors\",\n                                    children: \"New Project\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/projects\",\n                                    className: \"text-slate-300 hover:text-white transition-colors\",\n                                    children: \"Projects\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"ensimu-button text-sm py-2 px-4\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden text-white p-2\",\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-slate-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"text-slate-300 hover:text-white transition-colors\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/upload\",\n                                className: \"text-slate-300 hover:text-white transition-colors\",\n                                children: \"New Project\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/projects\",\n                                className: \"text-slate-300 hover:text-white transition-colors\",\n                                children: \"Projects\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"ensimu-button text-sm py-2 px-4 mt-4\",\n                                children: \"Get Started\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/layout/Navigation.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/providers.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFRTyxTQUFTQSxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ1pGOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2Vuc2ltdS1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy50c3g/YmU4NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xyXG5cclxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcclxuICBjaGlsZHJlbjogUmVhY3ROb2RlXHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiBQcm92aWRlcnNQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlblwiPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn0iXSwibmFtZXMiOlsiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/FeatureCards.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/FeatureCards.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeatureCards: () => (/* binding */ FeatureCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ FeatureCards auto */ \n\nconst features = [\n    {\n        icon: \"\\uD83D\\uDCE6\",\n        title: \"Smart Geometry Preparation\",\n        description: \"Our AI expertly guides you through defeaturing and simplification. Automatically identifies unnecessary features, suggests mid-surface creation, and optimizes your geometry for perfect simulation setup.\",\n        tags: [\n            \"Defeaturing\",\n            \"Mid-surfaces\",\n            \"Optimization\"\n        ],\n        color: \"from-blue-500 to-cyan-500\"\n    },\n    {\n        icon: \"\\uD83D\\uDD17\",\n        title: \"Intelligent Meshing\",\n        description: \"Get smart mesh recommendations tailored to your geometry complexity and physics requirements. Built-in quality assurance ensures optimal element quality and convergence.\",\n        tags: [\n            \"Quality Control\",\n            \"Adaptive\",\n            \"Convergence\"\n        ],\n        color: \"from-green-500 to-emerald-500\"\n    },\n    {\n        icon: \"\\uD83E\\uDDEC\",\n        title: \"Material Intelligence\",\n        description: \"Access our comprehensive material database with AI-powered property recommendations. Environment-aware selection considers temperature, stress, and safety factors automatically.\",\n        tags: [\n            \"Database\",\n            \"Safety Factors\",\n            \"Validation\"\n        ],\n        color: \"from-yellow-500 to-orange-500\"\n    },\n    {\n        icon: \"⚡\",\n        title: \"Physics Setup Assistant\",\n        description: \"Define boundary conditions and loads with confidence. Our physics assistant helps you set up realistic constraints, symmetries, and solver settings for accurate results.\",\n        tags: [\n            \"Boundary Conditions\",\n            \"Symmetry\",\n            \"Solvers\"\n        ],\n        color: \"from-purple-500 to-pink-500\"\n    }\n];\nfunction FeatureCards() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: index * 0.1\n                },\n                className: \"ensimu-card p-6 hover:scale-105 transition-all duration-300 group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-16 h-16 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl\",\n                                children: feature.icon\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/FeatureCards.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/FeatureCards.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-white mb-3\",\n                            children: feature.title\n                        }, void 0, false, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/FeatureCards.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-300 leading-relaxed mb-4\",\n                            children: feature.description\n                        }, void 0, false, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/FeatureCards.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 justify-center\",\n                            children: feature.tags.map((tag, tagIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 bg-slate-700/50 text-slate-300 text-sm rounded-full border border-slate-600/50\",\n                                    children: tag\n                                }, tagIndex, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/FeatureCards.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/FeatureCards.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/FeatureCards.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, index, false, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/FeatureCards.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/FeatureCards.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/FeatureCards.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/HeroSection.tsx":
/*!*************************************************!*\
  !*** ./src/components/sections/HeroSection.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroSection: () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ HeroSection auto */ \n\n\nfunction HeroSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.6\n        },\n        className: \"ensimu-card p-8 mb-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h1, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            className: \"text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent\",\n                            children: \"Welcome to Ensimu.space\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            className: \"text-xl md:text-2xl text-ensimu-300 mb-6\",\n                            children: \"AI-Powered CAE Preprocessing Platform\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            className: \"text-lg text-slate-300 leading-relaxed mb-8\",\n                            children: \"Transform complex engineering simulation setup with our intelligent multi-agent system. Our friendly AI assistants guide you through every step of geometry preparation, meshing, material assignment, and physics definition—making CAE preprocessing intuitive and efficient.\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.5\n                            },\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/upload\",\n                                    className: \"ensimu-button text-center\",\n                                    children: \"\\uD83D\\uDE80 Start Your First Project\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-3 border border-ensimu-500 text-ensimu-300 rounded-lg hover:bg-ensimu-500/10 transition-all duration-300\",\n                                    children: \"▶️ Watch Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.3\n                        },\n                        className: \"relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 h-64 bg-gradient-to-br from-ensimu-500/20 to-ensimu-700/20 rounded-full flex items-center justify-center pulse-glow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-32 h-32 bg-gradient-to-br from-ensimu-400 to-ensimu-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/HeroSection.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/HeroSection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/QuickStartGuide.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/QuickStartGuide.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickStartGuide: () => (/* binding */ QuickStartGuide)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ QuickStartGuide auto */ \n\nconst steps = [\n    {\n        number: 1,\n        title: \"Upload Your CAD Files\",\n        description: \"Support for STEP, IGES, STL, and more\",\n        color: \"bg-blue-500\"\n    },\n    {\n        number: 2,\n        title: \"Define Analysis Goals\",\n        description: \"Tell us what you want to simulate\",\n        color: \"bg-green-500\"\n    },\n    {\n        number: 3,\n        title: \"Let AI Guide You\",\n        description: \"Four specialized agents help with each step\",\n        color: \"bg-yellow-500\"\n    },\n    {\n        number: 4,\n        title: \"Export Ready Model\",\n        description: \"Get simulation-ready files for your solver\",\n        color: \"bg-purple-500\"\n    }\n];\nfunction QuickStartGuide() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        initial: {\n            opacity: 0,\n            x: 20\n        },\n        animate: {\n            opacity: 1,\n            x: 0\n        },\n        transition: {\n            duration: 0.6\n        },\n        className: \"ensimu-card p-6 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-bold text-white mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-2\",\n                        children: \"\\uD83D\\uDDFA️\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    \"Quick Start Guide\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: index * 0.1\n                        },\n                        className: \"flex items-start space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-8 h-8 ${step.color} rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0`,\n                                children: step.number\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-1\",\n                                        children: step.title\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-400 text-sm\",\n                                        children: step.description\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.5\n                },\n                className: \"w-full ensimu-button mt-6\",\n                children: \"\\uD83D\\uDE80 Get Started Now\"\n            }, void 0, false, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/QuickStartGuide.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/QuickStartGuide.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/StatsSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/StatsSection.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsSection: () => (/* binding */ StatsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ StatsSection auto */ \n\nconst stats = [\n    {\n        value: \"50+\",\n        label: \"Material Types\",\n        color: \"text-blue-400\"\n    },\n    {\n        value: \"85%\",\n        label: \"Time Reduction\",\n        color: \"text-green-400\"\n    },\n    {\n        value: \"10+\",\n        label: \"CAD Formats\",\n        color: \"text-yellow-400\"\n    },\n    {\n        value: \"24/7\",\n        label: \"AI Assistance\",\n        color: \"text-purple-400\"\n    }\n];\nfunction StatsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\",\n        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.9\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: index * 0.1\n                },\n                className: \"ensimu-card p-6 text-center hover:scale-105 transition-all duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h3, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: index * 0.1 + 0.2\n                        },\n                        className: `text-3xl font-bold ${stat.color} mb-2`,\n                        children: stat.value\n                    }, void 0, false, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/StatsSection.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-300 text-sm\",\n                        children: stat.label\n                    }, void 0, false, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/StatsSection.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/StatsSection.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/StatsSection.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/StatsSection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/WorkflowSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/WorkflowSection.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowSection: () => (/* binding */ WorkflowSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ WorkflowSection auto */ \n\nconst workflowSteps = [\n    {\n        step: 1,\n        title: \"Upload\",\n        description: \"Upload CAD files and define project objectives\",\n        icon: \"\\uD83D\\uDCE4\",\n        color: \"border-blue-500 bg-blue-500/10\"\n    },\n    {\n        step: 2,\n        title: \"Geometry\",\n        description: \"AI analyzes and recommends geometry preparation\",\n        icon: \"\\uD83D\\uDCE6\",\n        color: \"border-green-500 bg-green-500/10\"\n    },\n    {\n        step: 3,\n        title: \"Mesh & Materials\",\n        description: \"Intelligent meshing and material assignment\",\n        icon: \"\\uD83D\\uDD17\",\n        color: \"border-yellow-500 bg-yellow-500/10\"\n    },\n    {\n        step: 4,\n        title: \"Physics\",\n        description: \"Setup boundary conditions and export model\",\n        icon: \"⚡\",\n        color: \"border-purple-500 bg-purple-500/10\"\n    }\n];\nfunction WorkflowSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.section, {\n        initial: {\n            opacity: 0,\n            y: 40\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.8\n        },\n        className: \"mt-12 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-4 flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-3\",\n                                children: \"➡️\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            \"AI-Guided Preprocessing Workflow\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-300 max-w-2xl mx-auto\",\n                        children: \"Our intelligent system guides you through each step of the preprocessing workflow, ensuring optimal results with minimal effort.\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: workflowSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: index * 0.15\n                        },\n                        className: `ensimu-card p-6 text-center border-2 ${step.color} hover:scale-105 transition-all duration-300`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-4\",\n                                children: step.icon\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-2\",\n                                children: [\n                                    step.step,\n                                    \". \",\n                                    step.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 text-sm leading-relaxed\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.8\n                },\n                className: \"text-center mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"ensimu-button text-lg px-8 py-4\",\n                    children: \"\\uD83D\\uDE80 Start Your Workflow Now\"\n                }, void 0, false, {\n                    fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/sections/WorkflowSection.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/WorkflowSection.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"71ee74bb8e36\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW5zaW11LWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9mOTY2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzFlZTc0YmI4ZTM2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Ensimu.space - AI-Powered CAE Preprocessing\",\n    description: \"Transform complex engineering simulation setup with our intelligent multi-agent system.\",\n    keywords: \"CAE, preprocessing, AI, engineering, simulation, mesh, materials, physics\",\n    authors: [\n        {\n            name: \"Ensimu Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white min-h-screen`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/c/Users/<USER>/Downloads/EnsimuAgent/EnsimuAgent/ensimu-frontend/src/components/providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fc%2FUsers%2Fatomgetintheq%2FDownloads%2FEnsimuAgent%2FEnsimuAgent%2Fensimu-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();