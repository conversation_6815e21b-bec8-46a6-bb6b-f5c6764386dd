#!/usr/bin/env python3
"""
Test script to verify the EnsimuAgent to ensumu-space migration.
This script tests all the migrated components to ensure they work correctly.
"""

import os
import sys
import json
from pathlib import Path

# Add the backend to the path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

def test_imports():
    """Test that all migrated modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        from app.apis.cae_preprocessing import router
        print("✅ CAE Preprocessing API imported successfully")
    except Exception as e:
        print(f"❌ Failed to import CAE Preprocessing API: {e}")
        return False
    
    try:
        from app.libs.cae_agents import (
            GeometryAgent, MeshAgent, MaterialAgent, PhysicsAgent,
            SimPrepOrchestrator, get_agent_factory
        )
        print("✅ CAE Agents imported successfully")
    except Exception as e:
        print(f"❌ Failed to import CAE Agents: {e}")
        return False
    
    try:
        from app.libs.cae_models import (
            Project, UploadedFile, MaterialProperty, AISession,
            WorkflowExecution, HITLCheckpoint
        )
        print("✅ CAE Models imported successfully")
    except Exception as e:
        print(f"❌ Failed to import CAE Models: {e}")
        return False
    
    try:
        from app.libs.engineering_utils import (
            is_supported_cad_file, get_file_format_info,
            ENGINEERING_MATERIALS, get_solver_recommendations
        )
        print("✅ Engineering Utils imported successfully")
    except Exception as e:
        print(f"❌ Failed to import Engineering Utils: {e}")
        return False
    
    return True

def test_agent_factory():
    """Test the agent factory functionality."""
    print("\n🧪 Testing Agent Factory...")
    
    try:
        from app.libs.cae_agents import get_agent_factory
        
        factory = get_agent_factory()
        agents = factory.list_agents()
        
        expected_agents = ['geometry', 'mesh', 'materials', 'physics']
        for agent_type in expected_agents:
            if agent_type in agents:
                agent = factory.get_agent(agent_type)
                if agent:
                    print(f"✅ {agent_type.title()}Agent created successfully")
                else:
                    print(f"❌ Failed to create {agent_type}Agent")
                    return False
            else:
                print(f"❌ {agent_type}Agent not found in factory")
                return False
        
        orchestrator = factory.get_orchestrator()
        if orchestrator:
            print("✅ SimPrepOrchestrator created successfully")
        else:
            print("❌ Failed to create SimPrepOrchestrator")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agent factory test failed: {e}")
        return False

def test_engineering_utils():
    """Test engineering utilities."""
    print("\n🧪 Testing Engineering Utils...")
    
    try:
        from app.libs.engineering_utils import (
            is_supported_cad_file, get_file_format_info,
            ENGINEERING_MATERIALS, estimate_mesh_complexity,
            generate_preprocessing_checklist
        )
        
        # Test file format detection
        test_files = ["test.step", "test.iges", "test.stl", "test.obj", "test.txt"]
        for filename in test_files:
            is_supported = is_supported_cad_file(filename)
            format_info = get_file_format_info(filename)
            
            if filename.endswith('.txt'):
                if not is_supported:
                    print(f"✅ Correctly rejected unsupported file: {filename}")
                else:
                    print(f"❌ Incorrectly accepted unsupported file: {filename}")
                    return False
            else:
                if is_supported:
                    print(f"✅ Correctly accepted supported file: {filename} ({format_info['format']})")
                else:
                    print(f"❌ Incorrectly rejected supported file: {filename}")
                    return False
        
        # Test materials database
        if len(ENGINEERING_MATERIALS) >= 5:
            print(f"✅ Materials database loaded: {len(ENGINEERING_MATERIALS)} materials")
        else:
            print(f"❌ Materials database incomplete: {len(ENGINEERING_MATERIALS)} materials")
            return False
        
        # Test preprocessing checklist
        checklist = generate_preprocessing_checklist("step")
        if len(checklist) >= 6:
            print(f"✅ Preprocessing checklist generated: {len(checklist)} tasks")
        else:
            print(f"❌ Preprocessing checklist incomplete: {len(checklist)} tasks")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Engineering utils test failed: {e}")
        return False

def test_api_structure():
    """Test the API structure and endpoints."""
    print("\n🧪 Testing API Structure...")
    
    try:
        from app.apis.cae_preprocessing import router
        
        # Check that router has the expected endpoints
        routes = []
        for route in router.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        expected_endpoints = [
            '/projects',
            '/projects/{project_id}',
            '/projects/{project_id}/upload',
            '/projects/{project_id}/analyze',
            '/workflows/start',
            '/workflows/{workflow_id}/status',
            '/materials',
            '/health'
        ]
        
        for endpoint in expected_endpoints:
            # Check if any route matches the pattern
            found = any(endpoint.replace('{', '').replace('}', '') in route.replace('{', '').replace('}', '') for route in routes)
            if found:
                print(f"✅ Endpoint found: {endpoint}")
            else:
                print(f"❌ Endpoint missing: {endpoint}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ API structure test failed: {e}")
        return False

def test_frontend_components():
    """Test that frontend components exist."""
    print("\n🧪 Testing Frontend Components...")
    
    frontend_path = Path(__file__).parent / "frontend" / "src"
    
    # Check main CAE preprocessing page
    cae_page = frontend_path / "pages" / "CAEPreprocessingPage.tsx"
    if cae_page.exists():
        print("✅ CAEPreprocessingPage.tsx exists")
    else:
        print("❌ CAEPreprocessingPage.tsx missing")
        return False
    
    # Check UI components
    ui_components = [
        "button.tsx", "card.tsx", "progress.tsx", 
        "badge.tsx", "tabs.tsx", "alert.tsx"
    ]
    
    ui_path = frontend_path / "components" / "ui"
    for component in ui_components:
        component_path = ui_path / component
        if component_path.exists():
            print(f"✅ UI component exists: {component}")
        else:
            print(f"❌ UI component missing: {component}")
            return False
    
    # Check routes
    routes_file = frontend_path / "user-routes.tsx"
    if routes_file.exists():
        content = routes_file.read_text()
        if "CAEPreprocessingPage" in content and "/cae-preprocessing" in content:
            print("✅ CAE preprocessing routes configured")
        else:
            print("❌ CAE preprocessing routes not configured")
            return False
    else:
        print("❌ user-routes.tsx not found")
        return False
    
    return True

def main():
    """Run all tests."""
    print("🚀 Starting EnsimuAgent Migration Test Suite")
    print("=" * 50)
    
    # Set environment variable for OpenAI (mock)
    os.environ['OPENAI_API_KEY'] = 'test-key'
    
    tests = [
        test_imports,
        test_agent_factory,
        test_engineering_utils,
        test_api_structure,
        test_frontend_components
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test failed: {test.__name__}")
        except Exception as e:
            print(f"❌ Test error in {test.__name__}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Migration successful!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
