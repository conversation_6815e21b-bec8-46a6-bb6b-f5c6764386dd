"""
Production enhancements for the CAE preprocessing system.
Includes error handling, security, monitoring, and performance optimizations.
"""

import logging
import time
import asyncio
import hashlib
import secrets
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from functools import wraps
from contextlib import asynccontextmanager

import aiofiles
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cae_preprocessing.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class SecurityConfig:
    """Security configuration for production deployment."""
    
    # Rate limiting
    RATE_LIMIT_REQUESTS = 100  # requests per minute
    RATE_LIMIT_WINDOW = 60  # seconds
    
    # File upload limits
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS = {'.step', '.stp', '.iges', '.igs', '.stl', '.obj'}
    
    # API security
    API_KEY_LENGTH = 32
    TOKEN_EXPIRY_HOURS = 24
    
    # CORS settings
    ALLOWED_ORIGINS = ["http://localhost:3000", "https://yourdomain.com"]
    ALLOWED_METHODS = ["GET", "POST", "PUT", "DELETE"]
    ALLOWED_HEADERS = ["*"]

class RateLimiter:
    """Rate limiting implementation for API endpoints."""
    
    def __init__(self):
        self.requests: Dict[str, List[float]] = {}
    
    def is_allowed(self, client_id: str, limit: int = SecurityConfig.RATE_LIMIT_REQUESTS) -> bool:
        """Check if request is within rate limit."""
        now = time.time()
        window_start = now - SecurityConfig.RATE_LIMIT_WINDOW
        
        # Clean old requests
        if client_id in self.requests:
            self.requests[client_id] = [
                req_time for req_time in self.requests[client_id] 
                if req_time > window_start
            ]
        else:
            self.requests[client_id] = []
        
        # Check limit
        if len(self.requests[client_id]) >= limit:
            return False
        
        # Add current request
        self.requests[client_id].append(now)
        return True

# Global rate limiter instance
rate_limiter = RateLimiter()

class SecurityValidator:
    """Security validation utilities."""
    
    @staticmethod
    def validate_file_upload(filename: str, content: bytes) -> Dict[str, Any]:
        """Validate uploaded file for security."""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Check file extension
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
        if f'.{file_ext}' not in SecurityConfig.ALLOWED_EXTENSIONS:
            validation_result["valid"] = False
            validation_result["errors"].append(f"File extension .{file_ext} not allowed")
        
        # Check file size
        if len(content) > SecurityConfig.MAX_FILE_SIZE:
            validation_result["valid"] = False
            validation_result["errors"].append(f"File size exceeds {SecurityConfig.MAX_FILE_SIZE} bytes")
        
        # Check for suspicious content
        if b'<script' in content.lower() or b'javascript:' in content.lower():
            validation_result["valid"] = False
            validation_result["errors"].append("Suspicious content detected")
        
        # Validate CAD file headers
        if file_ext in ['step', 'stp']:
            if not content.startswith(b'ISO-10303'):
                validation_result["warnings"].append("Invalid STEP file header")
        elif file_ext in ['iges', 'igs']:
            if not content.startswith(b'START'):
                validation_result["warnings"].append("Invalid IGES file header")
        
        return validation_result
    
    @staticmethod
    def sanitize_input(user_input: str) -> str:
        """Sanitize user input to prevent injection attacks."""
        if not user_input:
            return ""
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '|', '`']
        sanitized = user_input
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        # Limit length
        return sanitized[:1000]
    
    @staticmethod
    def generate_api_key() -> str:
        """Generate secure API key."""
        return secrets.token_urlsafe(SecurityConfig.API_KEY_LENGTH)

class PerformanceMonitor:
    """Performance monitoring and metrics collection."""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.error_counts: Dict[str, int] = {}
    
    def record_request_time(self, endpoint: str, duration: float):
        """Record request processing time."""
        if endpoint not in self.metrics:
            self.metrics[endpoint] = []
        
        self.metrics[endpoint].append(duration)
        
        # Keep only last 1000 measurements
        if len(self.metrics[endpoint]) > 1000:
            self.metrics[endpoint] = self.metrics[endpoint][-1000:]
    
    def record_error(self, endpoint: str):
        """Record error occurrence."""
        self.error_counts[endpoint] = self.error_counts.get(endpoint, 0) + 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics summary."""
        summary = {}
        
        for endpoint, times in self.metrics.items():
            if times:
                summary[endpoint] = {
                    "avg_response_time": sum(times) / len(times),
                    "min_response_time": min(times),
                    "max_response_time": max(times),
                    "request_count": len(times),
                    "error_count": self.error_counts.get(endpoint, 0)
                }
        
        return summary

# Global performance monitor
performance_monitor = PerformanceMonitor()

class ErrorHandler:
    """Centralized error handling."""
    
    @staticmethod
    def handle_validation_error(error: Exception) -> HTTPException:
        """Handle validation errors."""
        logger.error(f"Validation error: {str(error)}")
        return HTTPException(
            status_code=400,
            detail=f"Validation error: {str(error)}"
        )
    
    @staticmethod
    def handle_file_error(error: Exception) -> HTTPException:
        """Handle file processing errors."""
        logger.error(f"File processing error: {str(error)}")
        return HTTPException(
            status_code=422,
            detail=f"File processing failed: {str(error)}"
        )
    
    @staticmethod
    def handle_agent_error(error: Exception) -> HTTPException:
        """Handle AI agent errors."""
        logger.error(f"AI agent error: {str(error)}")
        return HTTPException(
            status_code=503,
            detail="AI service temporarily unavailable. Please try again later."
        )
    
    @staticmethod
    def handle_database_error(error: Exception) -> HTTPException:
        """Handle database errors."""
        logger.error(f"Database error: {str(error)}")
        return HTTPException(
            status_code=500,
            detail="Database operation failed. Please try again later."
        )

def rate_limit_middleware(request: Request, call_next):
    """Rate limiting middleware."""
    client_ip = request.client.host
    
    if not rate_limiter.is_allowed(client_ip):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded. Please try again later."
        )
    
    return call_next(request)

def performance_middleware(request: Request, call_next):
    """Performance monitoring middleware."""
    start_time = time.time()
    
    try:
        response = call_next(request)
        duration = time.time() - start_time
        performance_monitor.record_request_time(request.url.path, duration)
        
        # Add performance headers
        response.headers["X-Response-Time"] = f"{duration:.3f}s"
        return response
        
    except Exception as e:
        performance_monitor.record_error(request.url.path)
        raise

def security_headers_middleware(request: Request, call_next):
    """Add security headers to responses."""
    response = call_next(request)
    
    # Security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Content-Security-Policy"] = "default-src 'self'"
    
    return response

class HealthChecker:
    """System health monitoring."""
    
    @staticmethod
    async def check_database_health() -> Dict[str, Any]:
        """Check database connectivity."""
        try:
            # Simulate database check
            await asyncio.sleep(0.1)
            return {"status": "healthy", "response_time": "0.1s"}
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    @staticmethod
    async def check_ai_service_health() -> Dict[str, Any]:
        """Check AI service availability."""
        try:
            # Simulate AI service check
            await asyncio.sleep(0.1)
            return {"status": "healthy", "response_time": "0.1s"}
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    @staticmethod
    async def check_file_system_health() -> Dict[str, Any]:
        """Check file system availability."""
        try:
            # Check if we can write to uploads directory
            test_file = "uploads/health_check.tmp"
            async with aiofiles.open(test_file, 'w') as f:
                await f.write("health check")
            
            # Clean up
            import os
            if os.path.exists(test_file):
                os.remove(test_file)
            
            return {"status": "healthy", "writable": True}
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    @staticmethod
    async def get_system_health() -> Dict[str, Any]:
        """Get comprehensive system health status."""
        health_checks = await asyncio.gather(
            HealthChecker.check_database_health(),
            HealthChecker.check_ai_service_health(),
            HealthChecker.check_file_system_health(),
            return_exceptions=True
        )
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "healthy" if all(
                check.get("status") == "healthy" for check in health_checks
                if isinstance(check, dict)
            ) else "degraded",
            "services": {
                "database": health_checks[0],
                "ai_service": health_checks[1],
                "file_system": health_checks[2]
            },
            "performance_metrics": performance_monitor.get_metrics()
        }

class CacheManager:
    """Simple in-memory cache for performance optimization."""
    
    def __init__(self, default_ttl: int = 300):  # 5 minutes default
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if key in self.cache:
            entry = self.cache[key]
            if datetime.utcnow() < entry["expires"]:
                return entry["value"]
            else:
                del self.cache[key]
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache."""
        ttl = ttl or self.default_ttl
        self.cache[key] = {
            "value": value,
            "expires": datetime.utcnow() + timedelta(seconds=ttl)
        }
    
    def clear_expired(self) -> None:
        """Clear expired cache entries."""
        now = datetime.utcnow()
        expired_keys = [
            key for key, entry in self.cache.items()
            if now >= entry["expires"]
        ]
        for key in expired_keys:
            del self.cache[key]

# Global cache instance
cache_manager = CacheManager()

def async_retry(max_retries: int = 3, delay: float = 1.0):
    """Decorator for async function retry logic."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}")
            
            logger.error(f"All {max_retries} attempts failed for {func.__name__}")
            raise last_exception
        
        return wrapper
    return decorator

class ConfigManager:
    """Environment-based configuration management."""
    
    @staticmethod
    def get_config() -> Dict[str, Any]:
        """Get configuration based on environment."""
        import os
        
        env = os.getenv("ENVIRONMENT", "development")
        
        base_config = {
            "debug": env == "development",
            "log_level": "DEBUG" if env == "development" else "INFO",
            "cors_origins": SecurityConfig.ALLOWED_ORIGINS,
            "rate_limit": SecurityConfig.RATE_LIMIT_REQUESTS,
            "max_file_size": SecurityConfig.MAX_FILE_SIZE
        }
        
        if env == "production":
            base_config.update({
                "cors_origins": ["https://yourdomain.com"],
                "rate_limit": 50,  # Stricter in production
                "require_https": True,
                "enable_monitoring": True
            })
        elif env == "staging":
            base_config.update({
                "cors_origins": ["https://staging.yourdomain.com"],
                "rate_limit": 75,
                "enable_monitoring": True
            })
        
        return base_config
