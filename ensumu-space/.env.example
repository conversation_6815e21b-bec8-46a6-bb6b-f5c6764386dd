# Environment Configuration for EnsumuSpace CAE Preprocessing
# Copy this file to .env and fill in your values

# Environment (development, staging, production)
ENVIRONMENT=development

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/ensumu_space
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration (for caching and session storage)
REDIS_URL=redis://localhost:6379/0

# File Storage Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600  # 100MB in bytes
ALLOWED_FILE_EXTENSIONS=.step,.stp,.iges,.igs,.stl,.obj

# Security Configuration
SECRET_KEY=your_secret_key_here_change_in_production
API_KEY_SALT=your_api_key_salt_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
CORS_ALLOW_CREDENTIALS=true

# Monitoring and Logging
LOG_LEVEL=INFO
ENABLE_METRICS=true
METRICS_PORT=9090

# AI Agent Configuration
AGENT_TIMEOUT_SECONDS=300
AGENT_MAX_RETRIES=3
AGENT_RETRY_DELAY=1.0

# Workflow Configuration
WORKFLOW_TIMEOUT_MINUTES=60
HITL_TIMEOUT_MINUTES=30
MAX_CONCURRENT_WORKFLOWS=10

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>

# External Services
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
FIREBASE_CLIENT_ID=your_firebase_client_id

# Performance Configuration
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your_backup_bucket

# Feature Flags
ENABLE_AI_AGENTS=true
ENABLE_WORKFLOW_ORCHESTRATION=true
ENABLE_HITL_CHECKPOINTS=true
ENABLE_MATERIAL_RECOMMENDATIONS=true
ENABLE_FILE_ANALYSIS=true
