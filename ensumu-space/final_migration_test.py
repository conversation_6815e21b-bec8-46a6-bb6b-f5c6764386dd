#!/usr/bin/env python3
"""
Final comprehensive test for the EnsimuAgent to ensumu-space migration.
This script validates all components and provides a migration completion report.
"""

import os
import sys
import json
import asyncio
from pathlib import Path
from datetime import datetime

def test_file_structure():
    """Test that all required files and directories exist."""
    print("🔍 Testing file structure...")
    
    required_files = [
        # Backend files
        "backend/app/apis/cae_preprocessing/__init__.py",
        "backend/app/libs/cae_agents.py",
        "backend/app/libs/cae_models.py", 
        "backend/app/libs/engineering_utils.py",
        "backend/app/libs/production_enhancements.py",
        "backend/pyproject.toml",
        "backend/main.py",
        
        # Frontend files
        "frontend/src/pages/CAEPreprocessingPage.tsx",
        "frontend/src/components/ui/button.tsx",
        "frontend/src/components/ui/card.tsx",
        "frontend/src/components/ui/progress.tsx",
        "frontend/src/components/ui/badge.tsx",
        "frontend/src/components/ui/tabs.tsx",
        "frontend/src/components/ui/alert.tsx",
        "frontend/src/user-routes.tsx",
        "frontend/package.json",
        
        # Configuration files
        ".env.example",
        "docker-compose.yml",
        "DEPLOYMENT_GUIDE.md",
        "README.md",
        
        # Test files
        "backend/tests/test_cae_integration.py",
        "test_migration.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print(f"✅ All {len(required_files)} required files present")
        return True

def test_backend_imports():
    """Test backend module imports."""
    print("\n🔍 Testing backend imports...")
    
    # Add backend to path
    backend_path = Path("backend")
    sys.path.insert(0, str(backend_path))
    
    try:
        # Test core imports
        from app.apis.cae_preprocessing import router
        print("✅ CAE Preprocessing API imported")
        
        from app.libs.cae_agents import get_agent_factory, GeometryAgent, MeshAgent, MaterialAgent, PhysicsAgent
        print("✅ AI Agents imported")
        
        from app.libs.cae_models import Project, UploadedFile, MaterialProperty, WorkflowExecution
        print("✅ Database models imported")
        
        from app.libs.engineering_utils import ENGINEERING_MATERIALS, is_supported_cad_file
        print("✅ Engineering utilities imported")
        
        from app.libs.production_enhancements import SecurityConfig, PerformanceMonitor, HealthChecker
        print("✅ Production enhancements imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_agent_functionality():
    """Test AI agent functionality."""
    print("\n🔍 Testing AI agent functionality...")
    
    try:
        from app.libs.cae_agents import get_agent_factory
        
        factory = get_agent_factory()
        agents = factory.list_agents()
        
        expected_agents = ['geometry', 'mesh', 'materials', 'physics']
        for agent_type in expected_agents:
            if agent_type in agents:
                agent = factory.get_agent(agent_type)
                if agent and hasattr(agent, 'analyze'):
                    print(f"✅ {agent_type.title()}Agent functional")
                else:
                    print(f"❌ {agent_type}Agent missing analyze method")
                    return False
            else:
                print(f"❌ {agent_type}Agent not found")
                return False
        
        # Test orchestrator
        orchestrator = factory.get_orchestrator()
        if orchestrator and hasattr(orchestrator, 'plan_workflow'):
            print("✅ SimPrepOrchestrator functional")
        else:
            print("❌ SimPrepOrchestrator missing plan_workflow method")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agent functionality test failed: {e}")
        return False

def test_engineering_database():
    """Test engineering materials database."""
    print("\n🔍 Testing engineering database...")
    
    try:
        from app.libs.engineering_utils import (
            ENGINEERING_MATERIALS, 
            get_material_by_name,
            search_materials_by_category,
            validate_material_properties
        )
        
        # Test materials database
        if len(ENGINEERING_MATERIALS) >= 5:
            print(f"✅ Materials database loaded: {len(ENGINEERING_MATERIALS)} materials")
        else:
            print(f"❌ Insufficient materials: {len(ENGINEERING_MATERIALS)}")
            return False
        
        # Test material search
        steel = get_material_by_name("Steel AISI 316L")
        if steel:
            print("✅ Material search functional")
        else:
            print("❌ Material search failed")
            return False
        
        # Test category search
        steels = search_materials_by_category("Stainless Steel")
        if steels:
            print("✅ Category search functional")
        else:
            print("❌ Category search failed")
            return False
        
        # Test validation
        validation = validate_material_properties(steel)
        if validation.get("valid"):
            print("✅ Material validation functional")
        else:
            print("❌ Material validation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Engineering database test failed: {e}")
        return False

def test_file_format_support():
    """Test file format support."""
    print("\n🔍 Testing file format support...")
    
    try:
        from app.libs.engineering_utils import (
            is_supported_cad_file,
            get_file_format_info,
            SUPPORTED_CAD_FORMATS
        )
        
        # Test supported formats
        supported_files = ["test.step", "test.iges", "test.stl", "test.obj"]
        unsupported_files = ["test.txt", "test.doc", "test.pdf"]
        
        for filename in supported_files:
            if is_supported_cad_file(filename):
                format_info = get_file_format_info(filename)
                if format_info.get("supported"):
                    print(f"✅ {filename} correctly supported")
                else:
                    print(f"❌ {filename} format info incorrect")
                    return False
            else:
                print(f"❌ {filename} incorrectly rejected")
                return False
        
        for filename in unsupported_files:
            if not is_supported_cad_file(filename):
                print(f"✅ {filename} correctly rejected")
            else:
                print(f"❌ {filename} incorrectly accepted")
                return False
        
        # Test format database
        if len(SUPPORTED_CAD_FORMATS) >= 4:
            print(f"✅ Format database complete: {len(SUPPORTED_CAD_FORMATS)} formats")
        else:
            print(f"❌ Format database incomplete: {len(SUPPORTED_CAD_FORMATS)} formats")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ File format test failed: {e}")
        return False

def test_frontend_integration():
    """Test frontend integration."""
    print("\n🔍 Testing frontend integration...")
    
    try:
        # Check main CAE page
        cae_page = Path("frontend/src/pages/CAEPreprocessingPage.tsx")
        if cae_page.exists():
            content = cae_page.read_text()
            if "CAEPreprocessingPage" in content and "useDropzone" in content:
                print("✅ CAE preprocessing page complete")
            else:
                print("❌ CAE preprocessing page incomplete")
                return False
        else:
            print("❌ CAE preprocessing page missing")
            return False
        
        # Check UI components
        ui_components = ["button", "card", "progress", "badge", "tabs", "alert"]
        ui_path = Path("frontend/src/components/ui")
        
        for component in ui_components:
            component_file = ui_path / f"{component}.tsx"
            if component_file.exists():
                print(f"✅ UI component {component} exists")
            else:
                print(f"❌ UI component {component} missing")
                return False
        
        # Check routing
        routes_file = Path("frontend/src/user-routes.tsx")
        if routes_file.exists():
            content = routes_file.read_text()
            if "CAEPreprocessingPage" in content and "/cae-preprocessing" in content:
                print("✅ Routing configured correctly")
            else:
                print("❌ Routing configuration incomplete")
                return False
        else:
            print("❌ Routes file missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Frontend integration test failed: {e}")
        return False

def test_production_readiness():
    """Test production readiness features."""
    print("\n🔍 Testing production readiness...")
    
    try:
        from app.libs.production_enhancements import (
            SecurityConfig, PerformanceMonitor, HealthChecker,
            SecurityValidator, RateLimiter, CacheManager
        )
        
        # Test security config
        if hasattr(SecurityConfig, 'RATE_LIMIT_REQUESTS') and hasattr(SecurityConfig, 'MAX_FILE_SIZE'):
            print("✅ Security configuration complete")
        else:
            print("❌ Security configuration incomplete")
            return False
        
        # Test performance monitor
        monitor = PerformanceMonitor()
        if hasattr(monitor, 'record_request_time') and hasattr(monitor, 'get_metrics'):
            print("✅ Performance monitoring functional")
        else:
            print("❌ Performance monitoring incomplete")
            return False
        
        # Test health checker
        if hasattr(HealthChecker, 'get_system_health'):
            print("✅ Health checking functional")
        else:
            print("❌ Health checking incomplete")
            return False
        
        # Test security validator
        validator = SecurityValidator()
        if hasattr(validator, 'validate_file_upload') and hasattr(validator, 'sanitize_input'):
            print("✅ Security validation functional")
        else:
            print("❌ Security validation incomplete")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Production readiness test failed: {e}")
        return False

def test_configuration_files():
    """Test configuration files."""
    print("\n🔍 Testing configuration files...")
    
    try:
        # Test environment example
        env_example = Path(".env.example")
        if env_example.exists():
            content = env_example.read_text()
            required_vars = ["OPENAI_API_KEY", "DATABASE_URL", "ENVIRONMENT"]
            if all(var in content for var in required_vars):
                print("✅ Environment configuration complete")
            else:
                print("❌ Environment configuration incomplete")
                return False
        else:
            print("❌ .env.example missing")
            return False
        
        # Test Docker configuration
        docker_compose = Path("docker-compose.yml")
        if docker_compose.exists():
            content = docker_compose.read_text()
            required_services = ["backend", "frontend", "db", "redis"]
            if all(service in content for service in required_services):
                print("✅ Docker configuration complete")
            else:
                print("❌ Docker configuration incomplete")
                return False
        else:
            print("❌ docker-compose.yml missing")
            return False
        
        # Test deployment guide
        deployment_guide = Path("DEPLOYMENT_GUIDE.md")
        if deployment_guide.exists():
            content = deployment_guide.read_text()
            if "Production Deployment" in content and "Security Configuration" in content:
                print("✅ Deployment guide complete")
            else:
                print("❌ Deployment guide incomplete")
                return False
        else:
            print("❌ DEPLOYMENT_GUIDE.md missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration files test failed: {e}")
        return False

def generate_migration_report():
    """Generate final migration report."""
    print("\n" + "="*60)
    print("📊 FINAL MIGRATION REPORT")
    print("="*60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Backend Imports", test_backend_imports),
        ("AI Agent Functionality", test_agent_functionality),
        ("Engineering Database", test_engineering_database),
        ("File Format Support", test_file_format_support),
        ("Frontend Integration", test_frontend_integration),
        ("Production Readiness", test_production_readiness),
        ("Configuration Files", test_configuration_files)
    ]
    
    results = {}
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results[test_name] = False
    
    print("\n" + "="*60)
    print("📈 TEST SUMMARY")
    print("="*60)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        print("✅ All EnsimuAgent functionality has been successfully integrated into ensumu-space")
        print("✅ Production-ready enhancements have been implemented")
        print("✅ System is ready for deployment")
    else:
        print(f"\n⚠️  MIGRATION PARTIALLY COMPLETE ({passed}/{total} tests passed)")
        print("❗ Please review failed tests and address issues before deployment")
    
    # Generate detailed report
    report = {
        "migration_date": datetime.now().isoformat(),
        "test_results": results,
        "success_rate": (passed/total)*100,
        "status": "COMPLETE" if passed == total else "PARTIAL",
        "components_migrated": [
            "4 AI Agents (Geometry, Mesh, Materials, Physics)",
            "SimPrep Orchestrator",
            "Human-in-the-Loop Workflows",
            "Material Database (5 materials)",
            "File Format Support (STEP, IGES, STL, OBJ)",
            "CAE Preprocessing API (15+ endpoints)",
            "React Frontend with UI Components",
            "Production Security Features",
            "Monitoring and Health Checks",
            "Docker Configuration",
            "Deployment Documentation"
        ],
        "production_features": [
            "Rate Limiting",
            "Input Validation", 
            "Error Handling",
            "Security Headers",
            "Performance Monitoring",
            "Health Checks",
            "Caching",
            "Logging",
            "Environment Configuration"
        ]
    }
    
    # Save report
    with open("migration_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: migration_report.json")
    
    return passed == total

if __name__ == "__main__":
    # Set environment for testing
    os.environ['OPENAI_API_KEY'] = 'test-key'
    
    success = generate_migration_report()
    sys.exit(0 if success else 1)
