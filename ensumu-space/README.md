# EnsumuSpace - AI-Powered CAE Preprocessing Platform

## 🚀 Overview

EnsumuSpace is a comprehensive AI-powered Computer-Aided Engineering (CAE) preprocessing platform that combines advanced simulation capabilities with intelligent automation. This platform integrates the sophisticated AI agent system from EnsimuAgent with the robust infrastructure of ensumu-space to deliver a production-ready SaaS solution.

## ✨ Key Features

### 🤖 AI-Powered Preprocessing
- **4 Specialized AI Agents**: Geometry, Mesh, Materials, and Physics agents
- **Intelligent Workflow Orchestration**: Automated preprocessing with human oversight
- **Human-in-the-Loop (HITL)**: Critical checkpoints for validation and approval
- **Smart Recommendations**: AI-driven suggestions for optimal simulation setup

### 🔧 Engineering Capabilities
- **Multi-Format CAD Support**: STEP, IGES, STL, OBJ file processing
- **Advanced Material Database**: 5+ engineering materials with comprehensive properties
- **Mesh Strategy Optimization**: Intelligent mesh generation recommendations
- **Physics Model Setup**: Automated boundary condition and solver configuration

### 🏗️ Production-Ready Architecture
- **FastAPI Backend**: High-performance async API with comprehensive error handling
- **React Frontend**: Modern, responsive UI with real-time updates
- **Security First**: Rate limiting, input validation, and secure file handling
- **Monitoring & Observability**: Health checks, metrics, and performance monitoring

## 🏛️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  FastAPI Backend │    │   AI Agents     │
│                 │    │                 │    │                 │
│ • CAE Interface │◄──►│ • REST API      │◄──►│ • GeometryAgent │
│ • File Upload   │    │ • Authentication│    │ • MeshAgent     │
│ • Workflow UI   │    │ • File Handling │    │ • MaterialAgent │
│ • Progress Track│    │ • Orchestration │    │ • PhysicsAgent  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │      Redis      │    │   File Storage  │
│                 │    │                 │    │                 │
│ • Projects      │    │ • Caching       │    │ • CAD Files     │
│ • Workflows     │    │ • Sessions      │    │ • Results       │
│ • Materials     │    │ • Rate Limiting │    │ • Backups       │
│ • HITL Data     │    │ • Temp Data     │    │ • Logs          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Node.js 18+ (for development)
- Python 3.11+ (for development)
- OpenAI API key

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ensumu-space
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start with Docker**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Stack

- React+Typescript frontend with `yarn` as package manager.
- Python FastAPI server with `uv` as package manager.

## Quickstart

1. Install dependencies:

```bash
make
```

2. Start the backend and frontend servers in separate terminals:

```bash
make run-backend
make run-frontend
```

## Gotchas

The backend server runs on port 8000 and the frontend development server runs on port 5173. The frontend Vite server proxies API requests to the backend on port 8000.

Visit <http://localhost:5173> to view the application.
